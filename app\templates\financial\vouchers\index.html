{% extends "financial/base.html" %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 用友风格凭证列表页面样式 */
.voucher-list-container {
    background: #f5f7fa;

}

.voucher-list-window {
    background: white;
    border: 1px solid #e0e0e0;
 
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
    margin: 0 auto;
   
}

.voucher-list-header {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border-bottom: 1px solid #90caf9;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: bold;
    color: #1565c0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-list-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;
    padding: 5px 10px;
    display: flex;
    gap: 5px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.toolbar-btn.primary {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.toolbar-btn.primary:hover {
    background: linear-gradient(to bottom, #1565c0, #0d47a1);
    color: white;
}

.voucher-search-bar {
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 13px;
}

.search-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-label {
    color: #666;
    font-weight: normal;
    white-space: nowrap;
}

.search-input {
    border: 1px solid #e0e0e0;
    padding: 2px 5px;
    font-size: 13px;
    background: white;
    border-radius: 3px;
}

.search-input:focus {
    border-color: #1e88e5;
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.1);
    outline: none;
}

.voucher-stats-bar {
    background: #e8f4fd;
    border-bottom: 1px solid #e0e0e0;
    padding: 6px 15px;
    display: flex;
    gap: 30px;
    align-items: center;
    font-size: 13px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #1565c0;
}

.voucher-list-table-container {
    padding: 0;
    background: white;
    overflow: auto;
}

.voucher-list-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin: 0;
    table-layout: fixed;
}

.voucher-list-table th {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    padding: 6px 4px;
    text-align: center;
    font-weight: normal;
    color: #1565c0;
    font-size: 13px;
    white-space: nowrap;
}

.voucher-list-table td {
    border: 1px solid #e0e0e0;
    padding: 6px 4px;
    vertical-align: middle;
    background: white;
    font-size: 13px;
    line-height: 1.6;
}

.voucher-list-table tbody tr:hover {
    background: #f5f5f5;
}

.voucher-list-table tbody tr.selected {
    background: #e3f2fd;
}

.voucher-number-link {
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 13px;
    font-weight: normal;
    color: #1565c0;
    text-decoration: none;
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;
    display: block;
    letter-spacing: 0.2px;
}

.voucher-number-link:hover {
    color: #0d47a1;
    text-decoration: underline;
    background: #f0f8ff;
}

.amount-cell {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    text-align: right;
    font-size: 13px;
    font-weight: bold;
    color: #000;
    padding: 2px 4px;
    letter-spacing: 0.5px;
}

.status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: normal;
    border: 1px solid;
    min-width: 40px;
    text-align: center;
    white-space: nowrap;
}

.status-badge.draft {
    background-color: #e0e0e0;
    color: #616161;
    border-color: #bdbdbd;
}

.status-badge.pending {
    background-color: #fff8e1;
    color: #ff8f00;
    border-color: #ffcc02;
}

.status-badge.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-color: #4caf50;
}

.status-badge.posted {
    background-color: #e3f2fd;
    color: #1565c0;
    border-color: #2196f3;
}

.action-buttons {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 5px;
    width: 100px;
    margin: 0 auto;
    height: 100%;
}

.action-btn {
    display: inline-flex;
    width: 22px;
    height: 22px;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    padding: 0;
    margin: 0;
    font-size: 13px;
    color: #666;
    cursor: pointer;
    text-decoration: none;
}

.action-btn:hover {
    background: #e8e8e8;
    color: #333;
    text-decoration: none;
}

.action-btn.view { color: #1976d2; }
.action-btn.edit { color: #f57c00; }
.action-btn.delete { color: #d32f2f; }

.voucher-pagination {
    background: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.page-link {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    text-decoration: none;
    min-width: 30px;
    text-align: center;
}

.page-link:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.page-link.active {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.page-link.disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

.status-bar {
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 4px 15px;
    font-size: 13px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .voucher-search-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .voucher-stats-bar {
        flex-wrap: wrap;
        gap: 15px;
    }

    .voucher-list-table {
        font-size: 13px;
    }

    .voucher-list-table th,
    .voucher-list-table td {
        padding: 2px 1px;
    }

    .action-btn {
        font-size: 13px;
        min-width: 14px;
        max-width: 16px;
        height: 14px;
        padding: 1px 2px;
    }

    .status-badge {
        font-size: 13px;
        padding: 1px 2px;
        min-width: 24px;
    }
}

/* 调整表格列宽 */
.voucher-list-table th:last-child,
.voucher-list-table td:last-child {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
    padding: 6px 2px;
    text-align: center;
}

/* 调整摘要列宽 */
.voucher-list-table th:nth-child(5),
.voucher-list-table td:nth-child(5) {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

/* 调整表格布局 */
.voucher-list-table {
    table-layout: fixed;
    width: 100%;
}

/* 调整摘要显示 */
.voucher-list-table td:nth-child(5) div {
    max-width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.6;
}

/* 用友风格整体表格样式 */
.voucher-list-table {
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 13px;
}

/* 用友风格凭证号样式 */
.voucher-number-cell {
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 13px;
    font-weight: normal;
    color: #000;
    background: #ffffff;
    border: 1px solid #e0e0e0;
}

/* 用友风格金额样式 */
.amount-cell {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    font-size: 13px;
    font-weight: bold;
    color: #000;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    text-align: right;
}

/* 用友经典货币符号 */
.uf-currency {
    font-family: 'Times New Roman', serif;
    font-weight: bold;
    color: #000;
}

/* Bootstrap风格凭证卡片样式 */
.voucher-card {
    transition: all 0.2s ease;
}

.voucher-card:hover {
    transform: translateY(-2px);
}

.voucher-card.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.voucher-card.selected .card {
    border-color: #2196f3 !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* 操作按钮样式 */
.voucher-actions {
    flex-shrink: 0;
}

.voucher-actions .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
    border-radius: 0.2rem;
}

/* 合计行样式 - 更大更粗的字体 */
.table tfoot td.fw-bold {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: #2e7d32 !important;
}

/* 科目代码和名称样式 */
.table tbody td .fw-bold {
    font-size: 0.85rem;
    color: #0d6efd;
}

.table tbody td .text-muted {
    font-size: 0.75rem;
    color: #6c757d !important;
}

/* 金额列样式 */
.font-monospace {
    font-family: 'Courier New', Consolas, monospace !important;
    font-weight: 500;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 操作按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 卡片头部样式 */
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
}

/* 凭证明细表格样式 - 覆盖用友主题 */
.voucher-detail-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border: 1px solid #e9ecef !important;
}

.voucher-detail-table th,
.voucher-detail-table td {
    border-bottom: 1px solid #e9ecef !important;
    vertical-align: middle !important;
}

/* 表头样式 - 更淡的背景色，覆盖用友主题 */
.voucher-header-row {
    background-color: #fafbfc !important;
    border-bottom: 1px solid #e9ecef !important;
}

.voucher-header-row th {
    font-weight: 600 !important;
    color: #000000 !important;
    background-color: #f0f0f0 !important;
    border-right: 1px solid #e9ecef !important;
    border: 1px solid #e9ecef !important;
    font-size: 13px !important;
}

.voucher-header-row th:last-child {
    border-right: none !important;
}

/* 明细行样式 - 覆盖用友主题 */
.voucher-detail-table tbody tr {
    background-color: #fdfdfd !important;
}

.voucher-detail-table tbody tr td {
    background-color: #fdfdfd !important;
    color: #333 !important;
    border-right: 1px solid #e9ecef !important;
    border: 1px solid #e9ecef !important;
    font-size: 13px !important;
}

.voucher-detail-table tbody tr td:last-child {
    border-right: none !important;
}

/* 合计行样式 - 更浅的背景色，覆盖用友主题 */
.voucher-total-row {
    background-color: #f1f3f4 !important;
}

.voucher-total-row td {
    background-color: #f1f3f4 !important;
    font-weight: 600 !important;
    color: #2e7d32 !important;
    border-right: 1px solid #e9ecef !important;
    border: 1px solid #e9ecef !important;
    font-size: 13px !important;
}

.voucher-total-row td:last-child {
    border-right: none !important;
}

/* 金额列右对齐 */
.voucher-detail-table td:nth-child(3),
.voucher-detail-table td:nth-child(4),
.voucher-detail-table th:nth-child(3),
.voucher-detail-table th:nth-child(4) {
    text-align: right;
}

/* 会计金额格式 */
.font-monospace {
    font-family: 'Times New Roman', 'Courier New', monospace !important;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 凭证列表内容区域 */
.voucher-list-content {
    padding: 0 15px;
    max-width: 100%;
    margin: 0 auto;
}

/* 凭证头部布局强制覆盖 */
.voucher-card .card-header .d-flex {
    align-items: center !important;
    height: 100% !important;
}

.voucher-card .card-header .d-flex > div:last-child {
    margin-left: 1rem !important;
    gap: 0.25rem !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .voucher-card .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }

    .voucher-actions {
        position: static !important;
        transform: none !important;
        margin: 0.5rem 0 0 0 !important;
        display: block !important;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

/* 打印样式 */
@media print {
    .voucher-list-header,
    .voucher-list-toolbar,
    .voucher-search-bar,
    .voucher-pagination,
    .status-bar,
    .voucher-actions {
        display: none !important;
    }

    .voucher-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .card {
        border: 1px solid #000 !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 0.25rem !important;
    }
}

/* ========================================
   关键样式覆盖 - 必须在最后加载以覆盖用友主题
   ======================================== */

/* 强制覆盖表头样式 - 最高优先级 */
.voucher-detail-table .voucher-header-row th {
    color: #000000 !important;
    background-color: #f0f0f0 !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    border: 1px solid #e9ecef !important;
    border-right: 1px solid #e9ecef !important;
}

/* 强制覆盖表格边框 - 最高优先级 */
.voucher-detail-table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border: 1px solid #e9ecef !important;
}

.voucher-detail-table th,
.voucher-detail-table td {
    border: 1px solid #e9ecef !important;
    vertical-align: middle !important;
    font-size: 13px !important;
}

/* 强制覆盖明细行样式 - 最高优先级 */
.voucher-detail-table tbody tr td {
    background-color: #fdfdfd !important;
    color: #333 !important;
    border-right: 1px solid #e9ecef !important;
}

/* 强制覆盖合计行样式 - 最高优先级 */
.voucher-detail-table .voucher-total-row td {
    background-color: #f1f3f4 !important;
    font-weight: 600 !important;
    color: #2e7d32 !important;
    border: 1px solid #e9ecef !important;
}

/* 强制覆盖选择框布局 - 最高优先级 */
.voucher-card .card-header .d-flex > div:first-child {
    width: 30px !important;
    flex-shrink: 0 !important;
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
}

.voucher-card .card-header .d-flex > div:nth-child(2) {
    width: 40px !important;
    flex-shrink: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.voucher-card .card-header .d-flex > div:nth-child(3) {
    margin-left: 0.5rem !important;
    flex-grow: 1 !important;
    display: flex !important;
    align-items: center !important;
    gap: 1.2rem !important;
}

</style>
{% endblock %}

{% block financial_content %}
<div class="voucher-list-container">
    <div class="voucher-list-window">
        <!-- 窗口标题栏 -->
        <div class="voucher-list-header">
            <span>财务管理</span>
            <div class="window-controls">
                <button class="toolbar-btn" onclick="location.href='{{ url_for('financial.reports_index') }}'">📊 报表</button>
                <button class="toolbar-btn" onclick="refreshPage()">🔄 刷新</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="voucher-list-toolbar">
            <a href="{{ url_for('financial.create_voucher') }}" class="toolbar-btn primary">
                ➕ 新建
            </a>
            <a href="{{ url_for('financial.vouchers_pending_stock_ins') }}" class="toolbar-btn">
                🔮 自动生成
            </a>
            <button type="button" class="toolbar-btn" onclick="showBatchModal()">
                📋 批量
            </button>
            <button type="button" class="toolbar-btn" onclick="showBatchReviewModal()" id="batchReviewBtn" style="display: none;">
                ✅ 批量审核
            </button>
            <button type="button" class="toolbar-btn" onclick="exportVouchers()">
                📤 导出
            </button>
            <div style="margin-left: auto;">
                <button type="button" class="toolbar-btn" onclick="showHelp()">
                    ❓ 帮助
                </button>
            </div>
        </div>
        <!-- 统计信息栏 -->
        <div class="voucher-stats-bar">
            <div class="stat-item">
                <span class="stat-label">总计:</span>
                <span class="stat-value">{{ vouchers.total }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">待审核:</span>
                <span class="stat-value" style="color: #ff8f00;">{{ pending_count or 0 }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">已审核:</span>
                <span class="stat-value" style="color: #2e7d32;">{{ approved_count or 0 }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">本月金额:</span>
                <span class="stat-value amount-cell">¥{{ "{:,.2f}".format(month_amount or 0) }}</span>
            </div>
        </div>

        <!-- 搜索筛选区 -->
        <div class="voucher-search-bar">
            <form method="GET" id="searchForm" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap; width: 100%;">
                <div class="search-group">
                    <span class="search-label">搜索:</span>
                    <input type="text" class="search-input" name="keyword" style="width: 120px;"
                           value="{{ keyword }}" placeholder="凭证号/摘要">
                </div>
                <div class="search-group">
                    <span class="search-label">类型:</span>
                    <select class="search-input" name="voucher_type" style="width: 80px;">
                        <option value="">全部</option>
                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款</option>
                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款</option>
                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账</option>
                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">状态:</span>
                    <select class="search-input" name="status" style="width: 70px;">
                        <option value="">全部</option>
                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">开始:</span>
                    <input type="date" class="search-input" name="start_date" style="width: 110px;"
                           value="{{ start_date }}">
                </div>
                <div class="search-group">
                    <span class="search-label">结束:</span>
                    <input type="date" class="search-input" name="end_date" style="width: 110px;"
                           value="{{ end_date }}">
                </div>
                <div class="search-group">
                    <button type="submit" class="toolbar-btn primary">
                        🔍 查询
                    </button>
                    <a href="{{ url_for('financial.vouchers_index') }}" class="toolbar-btn">
                        🔄 重置
                    </a>
                </div>
            </form>
        </div>

        <!-- Bootstrap风格凭证明细列表 -->
        {% if vouchers.items %}
        <div class="voucher-list-content">
            {% for voucher in vouchers.items %}
            <div class="voucher-card {% if loop.first %}mt-3{% endif %} mb-3" data-voucher-id="{{ voucher.id }}">
                <div class="card border-0 shadow-sm">
                    <!-- 凭证头部信息 -->
                    <div class="card-header border-bottom-0 voucher-header" style="background-color: #f8f9fa; padding: 0.5rem 0.75rem; height: 2.5rem;">
                        <div class="d-flex align-items-center h-100">
                            <!-- 选择框 -->
                            <div style="width: 30px; flex-shrink: 0; display: flex; justify-content: flex-start; align-items: center;">
                                <input type="checkbox" class="form-check-input voucher-checkbox" value="{{ voucher.id }}" style="margin: 0;">
                            </div>

                            <!-- 序号 -->
                            <div style="width: 40px; flex-shrink: 0; display: flex; justify-content: center; align-items: center;">
                                <span style="font-size: 14px; font-weight: bold; color: #333;">{{ loop.index + (vouchers.page - 1) * vouchers.per_page }}</span>
                            </div>

                            <!-- 信息内容 -->
                            <div class="d-flex align-items-center flex-grow-1" style="gap: 1.2rem; margin-left: 0.5rem;">
                                <span style="font-size: 14px; font-weight: bold; white-space: nowrap; color: #000;">
                                    月份: {{ voucher.voucher_date.strftime('%m') }}
                                </span>
                                <span style="font-size: 14px; font-weight: bold; white-space: nowrap; color: #000;">
                                    制单人: {{ voucher.creator.username if voucher.creator else '未知' }}
                                </span>
                                <span style="font-size: 14px; font-weight: bold; white-space: nowrap; color: #000;">
                                    <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" style="color: #000; text-decoration: none;">
                                        凭证字号: {{ voucher.voucher_number }}
                                    </a>
                                </span>
                                <span style="font-size: 14px; font-weight: bold; white-space: nowrap; color: #000;">
                                    {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                    <a href="#" onclick="viewAttachments({{ voucher.id }})" style="color: #000; text-decoration: none;">
                                        附单据 {{ voucher.attachment_count }} 张
                                    </a>
                                    {% else %}
                                    附单据 0 张
                                    {% endif %}
                                </span>
                                <span style="white-space: nowrap;">
                                    {% if voucher.status == '草稿' %}
                                        <span class="badge bg-secondary" style="font-size: 12px; font-weight: bold;">草稿</span>
                                    {% elif voucher.status == '待审核' %}
                                        <span class="badge bg-warning text-dark" style="font-size: 12px; font-weight: bold;">待审</span>
                                    {% elif voucher.status == '已审核' %}
                                        <span class="badge bg-success" style="font-size: 12px; font-weight: bold;">已审</span>
                                    {% elif voucher.status == '已记账' %}
                                        <span class="badge bg-primary" style="font-size: 12px; font-weight: bold;">已记</span>
                                    {% endif %}
                                </span>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex align-items-center" style="gap: 0.25rem; margin-left: 1rem;">
                                <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" class="btn btn-outline-primary btn-sm" title="编辑" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-outline-info btn-sm" title="查看" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="previewVoucher({{ voucher.id }})" title="预览" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-outline-dark btn-sm" onclick="printVoucher({{ voucher.id }})" title="打印" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-print"></i>
                                </button>
                                {% if voucher.status == '草稿' %}
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="submitVoucherForReview({{ voucher.id }})" title="提交审核" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                                {% endif %}
                                {% if voucher.status == '待审核' %}
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="reviewVoucher({{ voucher.id }})" title="审核通过" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="rejectVoucher({{ voucher.id }})" title="审核拒绝" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                                {% if voucher.status == '已审核' %}
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="cancelReviewVoucher({{ voucher.id }})" title="取消审核" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-undo"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="copyVoucher({{ voucher.id }})" title="复制" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-copy"></i>
                                </button>
                                {% if voucher.status in ['草稿', '待审核'] %}
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteVoucher({{ voucher.id }})" title="删除" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 凭证明细表格 -->
                    <div class="card-body p-0">
                        <table class="table table-sm mb-0 voucher-detail-table" style="font-size: 0.875rem;">
                            <thead>
                                <tr class="voucher-header-row" style="height: 2.5rem;">
                                    <th style="width: 35%; padding: 0.5rem 0.5rem; font-size: 0.8rem; font-weight: 600;">摘要</th>
                                    <th style="width: 25%; padding: 0.5rem 0.5rem; font-size: 0.8rem; font-weight: 600;">科目</th>
                                    <th style="width: 20%; padding: 0.5rem 0.5rem; font-size: 0.8rem; font-weight: 600; text-align: right;">借方金额</th>
                                    <th style="width: 20%; padding: 0.5rem 0.5rem; font-size: 0.8rem; font-weight: 600; text-align: right;">贷方金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if voucher.details_list %}
                                    {% for detail in voucher.details_list %}
                                    <tr style="height: 2.5rem;">
                                        <td style="padding: 0.375rem 0.5rem; vertical-align: middle;">{{ detail.summary or '' }}</td>
                                        <td style="padding: 0.375rem 0.5rem; vertical-align: middle;">
                                            <div class="fw-bold text-primary" style="font-size: 0.8rem; line-height: 1.2;">{{ detail.subject.code if detail.subject else '未知' }}</div>
                                            <div class="text-muted" style="font-size: 0.7rem; line-height: 1.1;">{{ detail.subject.name if detail.subject else '未知科目' }}</div>
                                        </td>
                                        <td style="padding: 0.375rem 0.5rem; vertical-align: middle; text-align: right; font-size: 13px;" class="font-monospace">
                                            {% if detail.debit_amount > 0 %}{{ "{:,.2f}".format(detail.debit_amount) }}{% else %}&nbsp;{% endif %}
                                        </td>
                                        <td style="padding: 0.375rem 0.5rem; vertical-align: middle; text-align: right; font-size: 13px;" class="font-monospace">
                                            {% if detail.credit_amount > 0 %}{{ "{:,.2f}".format(detail.credit_amount) }}{% else %}&nbsp;{% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr style="height: 3rem;">
                                        <td colspan="4" class="text-center text-muted" style="padding: 1rem;">
                                            <em>暂无明细数据</em>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                            <!-- 合计行 -->
                            <tfoot>
                                <tr class="voucher-total-row" style="height: 2.5rem;">
                                    <td colspan="2" style="padding: 0.5rem; vertical-align: middle; font-size: 0.875rem;" class="fw-bold">合计</td>
                                    <td style="padding: 0.5rem; vertical-align: middle; text-align: right; font-size: 13px;" class="fw-bold font-monospace">
                                        {{ "{:,.2f}".format(voucher.details_list|sum(attribute='debit_amount')) }}
                                    </td>
                                    <td style="padding: 0.5rem; vertical-align: middle; text-align: right; font-size: 13px;" class="fw-bold font-monospace">
                                        {{ "{:,.2f}".format(voucher.details_list|sum(attribute='credit_amount')) }}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if vouchers.pages > 1 %}
        <div class="voucher-pagination">
            {% if vouchers.has_prev %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">◀ 上页</a>
            {% endif %}

            {% for page_num in vouchers.iter_pages() %}
                {% if page_num %}
                    {% if page_num != vouchers.page %}
                    <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                    {% else %}
                    <span class="page-link active">{{ page_num }}</span>
                    {% endif %}
                {% else %}
                <span class="page-link disabled">…</span>
                {% endif %}
            {% endfor %}

            {% if vouchers.has_next %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下页 ▶</a>
            {% endif %}
        </div>
        {% endif %}
        {% else %}
        <div style="padding: 40px; text-align: center; color: #666; background: white;">
            <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
            <div>暂无财务凭证数据</div>
            <div style="font-size: 13px; margin-top: 5px;">点击"新建"按钮创建第一张凭证</div>
        </div>
        {% endif %}

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>共 {{ vouchers.total }} 条记录</span>
            <span id="current-time"></span>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量审核模态框 -->
<div class="modal fade" id="batchReviewModal" tabindex="-1" role="dialog" aria-labelledby="batchReviewModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchReviewModalLabel">批量审核财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量审核说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>只能审核"待审核"状态的凭证</li>
                        <li>系统会自动检查凭证借贷平衡</li>
                        <li>不平衡的凭证将跳过审核</li>
                        <li>审核通过后凭证状态变为"已审核"</li>
                    </ul>
                </div>

                <div id="selectedVouchersInfo" class="mb-3">
                    <strong>已选择凭证：</strong>
                    <span id="selectedVouchersCount">0</span> 个
                    <div id="selectedVouchersList" class="mt-2" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                        <!-- 选中的凭证列表 -->
                    </div>
                </div>

                <div class="form-group">
                    <label>审核操作：</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="reviewAction" id="approveAction" value="approve" checked>
                        <label class="form-check-label" for="approveAction">
                            <i class="fas fa-check text-success"></i> 审核通过
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="reviewAction" id="rejectAction" value="reject">
                        <label class="form-check-label" for="rejectAction">
                            <i class="fas fa-times text-danger"></i> 审核拒绝
                        </label>
                    </div>
                </div>

                <div class="form-group" id="rejectReasonGroup" style="display: none;">
                    <label for="rejectReason">拒绝原因：</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请填写拒绝原因..."></textarea>
                </div>

                <div id="batchReviewProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="reviewProgressText">准备审核...</small>
                    </div>
                </div>

                <div id="batchReviewResults" style="display: none;">
                    <h6>审核结果：</h6>
                    <div id="reviewResultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchReview">开始审核</button>
            </div>
        </div>
    </div>
</div>

<!-- 单个凭证拒绝审核模态框 -->
<div class="modal fade" id="rejectVoucherModal" tabindex="-1" role="dialog" aria-labelledby="rejectVoucherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectVoucherModalLabel">拒绝审核凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="rejectVoucherForm" method="POST">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        确定要拒绝审核此凭证吗？凭证将退回到草稿状态。
                    </div>
                    <div class="form-group">
                        <label for="singleRejectReason">拒绝原因：</label>
                        <textarea class="form-control" name="reject_reason" id="singleRejectReason" rows="3" placeholder="请填写拒绝原因..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认拒绝</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}



{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 用友风格财务软件 - 凭证列表页面

// URL模板 - 避免硬编码，使用现有路由
const URL_TEMPLATES = {
    viewVoucher: '/financial/vouchers/{id}',
    editVoucher: '/financial/vouchers/{id}/edit',
    reviewVoucher: '/financial/vouchers/{id}/review',
    rejectVoucher: '/financial/vouchers/{id}/reject',
    cancelReview: '/financial/vouchers/{id}/cancel-review',
    submitReview: '/financial/vouchers/{id}/submit-review',
    deleteVoucher: '/financial/vouchers/{id}/delete',
    textView: '/financial/vouchers/{id}/text-view',
    batchGenerate: '/financial/vouchers/batch-generate-from-stock-ins',
    batchReview: '/financial/vouchers/batch-review',
    createVoucher: '/financial/vouchers/create',
    copyVoucher: '/financial/vouchers/{id}/copy'
};

$(document).ready(function() {
    initUFVoucherList();
    bindUFEvents();
    updateTime();
    setInterval(updateTime, 1000);
});

// 更新时间显示
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString();
    document.getElementById('current-time').textContent = timeString;
}

// 刷新页面
function refreshPage() {
    window.location.reload();
}

// 显示帮助
function showHelp() {
    alert('用友风格凭证管理系统\n\n快捷键：\nCtrl+A: 全选\nDelete: 删除选中\nF5: 刷新页面\n双击行: 编辑凭证\n点击凭证号: 编辑凭证');
}

// 初始化Bootstrap风格凭证列表
function initUFVoucherList() {
    console.log('初始化Bootstrap风格凭证列表');

    // 全选功能
    $('#selectAll').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', isChecked);
        updateSelectedRows();
        updateStatusBar();
    });

    // 单个复选框
    $('.voucher-checkbox').on('change', function() {
        const $card = $(this).closest('.voucher-card');
        if ($(this).prop('checked')) {
            $card.addClass('selected');
        } else {
            $card.removeClass('selected');
        }

        const total = $('.voucher-checkbox').length;
        const checked = $('.voucher-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
        updateStatusBar();
        updateBatchActions();
    });

    // 卡片点击选择
    $('.voucher-card').on('click', function(e) {
        if (e.target.type !== 'checkbox' && !$(e.target).is('a') && !$(e.target).is('button') && !$(e.target).is('i')) {
            const $checkbox = $(this).find('.voucher-checkbox');
            $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
        }
    });

    // 双击编辑凭证
    $('.voucher-card').on('dblclick', function() {
        const voucherId = $(this).find('.voucher-checkbox').val();
        if (voucherId) {
            window.location.href = URL_TEMPLATES.editVoucher.replace('{id}', voucherId);
        }
    });
}

// 绑定用友风格事件
function bindUFEvents() {
    // 搜索表单自动提交
    $('select[name="voucher_type"], select[name="status"]').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车搜索
    $('input[name="keyword"]').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });

    // 快捷键支持
    $(document).on('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            $('#selectAll').prop('checked', true).trigger('change');
        }

        // Delete 删除选中
        if (e.key === 'Delete') {
            const selectedIds = getSelectedVoucherIds();
            if (selectedIds.length > 0) {
                batchDeleteVouchers(selectedIds);
            }
        }

        // F5 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            window.location.reload();
        }
    });
}

// 更新选中卡片样式
function updateSelectedRows() {
    $('.voucher-checkbox').each(function() {
        const $card = $(this).closest('.voucher-card');
        if ($(this).prop('checked')) {
            $card.addClass('selected');
        } else {
            $card.removeClass('selected');
        }
    });
}

// 更新状态栏
function updateStatusBar() {
    const total = $('.voucher-checkbox').length;
    const selected = $('.voucher-checkbox:checked').length;
    const statusText = selected > 0 ?
        `共 ${total} 条记录，已选择 ${selected} 条` :
        `共 ${total} 条记录`;

    // 更新状态栏文本
    $('.status-bar span:first-child').text(statusText);
}

// 获取选中的凭证ID
function getSelectedVoucherIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新批量操作状态
function updateBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    const batchReviewBtn = document.getElementById('batchReviewBtn');

    // 检查是否有待审核的凭证被选中
    const pendingVouchers = $('.voucher-checkbox:checked').filter(function() {
        const row = $(this).closest('tr');
        const status = row.find('.status-badge').text().trim();
        return status === '待审';
    });

    if (pendingVouchers.length > 0) {
        batchReviewBtn.style.display = 'inline-block';
    } else {
        batchReviewBtn.style.display = 'none';
    }
}

// 批量操作模态框
function showBatchModal() {
    const checkedIds = getSelectedIds();
    if (checkedIds.length === 0) {
        alert('请先选择凭证');
        return;
    }
    alert(`已选择 ${checkedIds.length} 个凭证，批量操作功能开发中...`);
}

// 导出凭证
function exportVouchers() {
    alert('导出功能开发中...');
}

// 获取选中的凭证ID
function getSelectedIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 预览凭证
function previewVoucher(id) {
    const previewUrl = `/financial/vouchers/${id}/print?preview=1`;
    window.open(previewUrl, '_blank', 'width=1000,height=700,scrollbars=yes');
}

// 打印凭证
function printVoucher(id) {
    const printUrl = `/financial/vouchers/${id}/print?auto_print=1`;
    window.open(printUrl, '_blank', 'width=800,height=600');
}

// 凭证操作
function reviewVoucher(id) {
    if (confirm('确定审核此凭证？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = URL_TEMPLATES.reviewVoucher.replace('{id}', id);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectVoucher(id) {
    // 设置表单action
    const form = document.getElementById('rejectVoucherForm');
    form.action = URL_TEMPLATES.rejectVoucher.replace('{id}', id);

    // 清空拒绝原因
    document.getElementById('singleRejectReason').value = '';

    // 显示模态框
    $('#rejectVoucherModal').modal('show');
}

// 取消审核凭证
function cancelReviewVoucher(voucherId) {
    if (!confirm('确定要取消审核此凭证吗？凭证将退回到待审核状态。')) {
        return;
    }

    const url = URL_TEMPLATES.cancelReview.replace('{id}', voucherId);

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('取消审核成功');
            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('取消审核失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('取消审核失败:', error);
        alert('取消审核失败，请重试');
    });
}

function copyVoucher(id) {
    if (confirm('确定复制此凭证？将创建一个新的草稿凭证')) {
        // 跳转到复制凭证页面，该页面会创建新凭证并进入编辑模式
        window.location.href = URL_TEMPLATES.copyVoucher.replace('{id}', id);
    }
}

function deleteVoucher(id) {
    // 先检查引用关系
    fetch(`/financial/vouchers/${id}/check-references`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.references.length > 0) {
                    // 有引用关系，显示详细信息
                    let referenceInfo = `凭证 ${data.voucher_number} 被以下记录引用：\n\n`;
                    data.references.forEach(ref => {
                        referenceInfo += `• ${ref.type}: ${ref.number} (${ref.date}) - ¥${ref.amount.toLocaleString()}\n`;
                    });
                    referenceInfo += `\n删除此凭证将自动解除这些引用关系。\n确定要继续删除吗？`;

                    if (confirm(referenceInfo)) {
                        performDelete(id);
                    }
                } else {
                    // 无引用关系，直接确认删除
                    if (confirm('确定删除此凭证？此操作不可恢复！')) {
                        performDelete(id);
                    }
                }
            } else {
                alert('检查引用关系失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('检查引用关系失败:', error);
            // 如果检查失败，使用原来的简单确认方式
            if (confirm('确定删除此凭证？此操作不可恢复！')) {
                performDelete(id);
            }
        });
}

function performDelete(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = URL_TEMPLATES.deleteVoucher.replace('{id}', id);
    document.body.appendChild(form);
    form.submit();
}

// 批量删除凭证
function batchDeleteVouchers(ids) {
    if (confirm(`确定删除选中的 ${ids.length} 个凭证？此操作不可恢复！`)) {
        // 逐个删除选中的凭证
        let deleteCount = 0;
        ids.forEach(id => {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = URL_TEMPLATES.deleteVoucher.replace('{id}', id);
            document.body.appendChild(form);
            form.submit();
            deleteCount++;
        });

        // 提示用户
        alert(`已提交 ${deleteCount} 个删除请求，页面将刷新...`);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch(URL_TEMPLATES.batchGenerate, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}

// 显示批量审核模态框
function showBatchReviewModal() {
    const selectedIds = getSelectedVoucherIds();
    if (selectedIds.length === 0) {
        alert('请先选择要审核的凭证');
        return;
    }

    // 过滤出待审核的凭证
    const pendingVouchers = [];
    $('.voucher-checkbox:checked').each(function() {
        const row = $(this).closest('tr');
        const status = row.find('.status-badge').text().trim();
        const voucherNumber = row.find('td:nth-child(3)').text().trim();
        const voucherDate = row.find('td:nth-child(4)').text().trim();
        const summary = row.find('td:nth-child(5)').text().trim();

        if (status === '待审') {
            pendingVouchers.push({
                id: $(this).val(),
                number: voucherNumber,
                date: voucherDate,
                summary: summary
            });
        }
    });

    if (pendingVouchers.length === 0) {
        alert('选中的凭证中没有待审核的凭证');
        return;
    }

    // 更新模态框内容
    document.getElementById('selectedVouchersCount').textContent = pendingVouchers.length;

    const vouchersList = document.getElementById('selectedVouchersList');
    vouchersList.innerHTML = pendingVouchers.map(v =>
        `<div class="mb-1">
            <small class="text-muted">${v.number}</small> - ${v.date} - ${v.summary}
        </div>`
    ).join('');

    // 重置表单
    document.getElementById('approveAction').checked = true;
    document.getElementById('rejectReason').value = '';
    document.getElementById('rejectReasonGroup').style.display = 'none';

    // 重置状态
    document.getElementById('batchReviewProgress').style.display = 'none';
    document.getElementById('batchReviewResults').style.display = 'none';
    document.getElementById('startBatchReview').style.display = 'inline-block';

    // 显示模态框
    $('#batchReviewModal').modal('show');
}

// 审核操作切换
$(document).ready(function() {
    $('input[name="reviewAction"]').on('change', function() {
        const rejectReasonGroup = document.getElementById('rejectReasonGroup');
        if ($(this).val() === 'reject') {
            rejectReasonGroup.style.display = 'block';
        } else {
            rejectReasonGroup.style.display = 'none';
        }
    });

    // 批量审核按钮事件
    $('#startBatchReview').on('click', function() {
        const selectedIds = [];
        $('.voucher-checkbox:checked').each(function() {
            const row = $(this).closest('tr');
            const status = row.find('.status-badge').text().trim();
            if (status === '待审') {
                selectedIds.push($(this).val());
            }
        });

        if (selectedIds.length === 0) {
            alert('没有可审核的凭证');
            return;
        }

        const action = $('input[name="reviewAction"]:checked').val();
        const rejectReason = document.getElementById('rejectReason').value.trim();

        if (action === 'reject' && !rejectReason) {
            alert('拒绝审核时必须填写原因');
            return;
        }

        // 开始批量审核
        startBatchReview(selectedIds, action, rejectReason);
    });
});

// 开始批量审核
function startBatchReview(voucherIds, action, rejectReason = '') {
    // 隐藏按钮，显示进度
    document.getElementById('startBatchReview').style.display = 'none';
    document.getElementById('batchReviewProgress').style.display = 'block';

    const progressBar = document.querySelector('#batchReviewModal .progress-bar');
    const progressText = document.getElementById('reviewProgressText');

    progressText.textContent = '正在批量审核凭证...';
    progressBar.style.width = '50%';
    progressBar.textContent = '50%';

    // 发送批量审核请求
    fetch(URL_TEMPLATES.batchReview, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            voucher_ids: voucherIds,
            action: action,
            reject_reason: rejectReason
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '审核完成！';

        // 显示结果
        document.getElementById('batchReviewProgress').style.display = 'none';
        document.getElementById('batchReviewResults').style.display = 'block';

        const resultsContent = document.getElementById('reviewResultsContent');
        if (data.success) {
            const actionText = action === 'approve' ? '审核通过' : '审核拒绝';
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量${actionText}完成</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                    </ul>
                    ${data.failed_count > 0 ? `
                        <p><strong>失败原因：</strong></p>
                        <ul>
                            ${data.failed_reasons.map(reason => `<li>${reason}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量审核失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '审核失败：' + error.message;

        document.getElementById('batchReviewResults').style.display = 'block';
        document.getElementById('reviewResultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
