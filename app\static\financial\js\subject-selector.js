/**
 * 会计科目选择器
 * 支持编码、名称、拼音首字母快速输入
 */

// 科目输入处理
function handleSubjectInput(e) {
    const $input = $(e.target);
    const searchText = $input.val().trim();
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');
    const $list = $dropdown.find('.uf-subject-dropdown-list');

    if (!searchText) {
        buildUFSubjectTable($list);
        $dropdown.show();
        return;
    }

    // 用友风格搜索：支持编码、名称、拼音首字母
    filteredSubjects = filterSubjects(subjects, searchText);

    console.log(`🔍 搜索 "${searchText}": 找到 ${filteredSubjects.length} 个匹配科目`);

    if (filteredSubjects.length === 0) {
        $list.html('<div class="uf-subject-no-results">未找到匹配的科目</div>');
        $dropdown.show();
        return;
    }

    // 显示搜索结果
    buildFilteredSubjectList($list, filteredSubjects, searchText);
    $dropdown.show();
}

// 科目输入框获得焦点
function handleSubjectFocus(e) {
    const $input = $(e.target);
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');
    const $list = $dropdown.find('.uf-subject-dropdown-list');

    // 显示科目选择器
    if (!$input.val().trim()) {
        buildUFSubjectTable($list);
    } else {
        handleSubjectInput(e);
    }
    
    $dropdown.show();
    
    // 设置输入框样式
    $input.addClass('focused');
}

// 科目输入框失去焦点
function handleSubjectBlur(e) {
    const $input = $(e.target);
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');

    // 延迟隐藏，允许点击选项
    setTimeout(() => {
        $dropdown.hide();
        $input.removeClass('focused');
    }, 200);
}

// 科目输入框键盘事件
function handleSubjectKeydown(e) {
    const $input = $(e.target);
    const $container = $input.closest('.uf-subject-selector');
    const $dropdown = $container.find('.uf-subject-dropdown');
    const $options = $dropdown.find('.uf-subject-option');
    const $selected = $options.filter('.selected');

    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            if ($selected.length === 0) {
                $options.first().addClass('selected');
            } else {
                const $next = $selected.removeClass('selected').next('.uf-subject-option');
                if ($next.length > 0) {
                    $next.addClass('selected');
                } else {
                    $options.first().addClass('selected');
                }
            }
            scrollToSelected($dropdown);
            break;

        case 'ArrowUp':
            e.preventDefault();
            if ($selected.length === 0) {
                $options.last().addClass('selected');
            } else {
                const $prev = $selected.removeClass('selected').prev('.uf-subject-option');
                if ($prev.length > 0) {
                    $prev.addClass('selected');
                } else {
                    $options.last().addClass('selected');
                }
            }
            scrollToSelected($dropdown);
            break;

        case 'Enter':
            e.preventDefault();
            if ($selected.length > 0) {
                selectSubject($selected);
            } else if ($options.length === 1) {
                selectSubject($options.first());
            }
            break;

        case 'Escape':
            e.preventDefault();
            $dropdown.hide();
            break;

        case 'Tab':
            // Tab键自动选择第一个匹配项
            if ($options.length === 1) {
                e.preventDefault();
                selectSubject($options.first());
            }
            break;
    }
}

// 滚动到选中项
function scrollToSelected($dropdown) {
    const $selected = $dropdown.find('.uf-subject-option.selected');
    if ($selected.length > 0) {
        const dropdownHeight = $dropdown.height();
        const selectedTop = $selected.position().top;
        const selectedHeight = $selected.outerHeight();
        
        if (selectedTop < 0) {
            $dropdown.scrollTop($dropdown.scrollTop() + selectedTop);
        } else if (selectedTop + selectedHeight > dropdownHeight) {
            $dropdown.scrollTop($dropdown.scrollTop() + selectedTop + selectedHeight - dropdownHeight);
        }
    }
}

// 构建科目表格（显示所有科目）
function buildUFSubjectTable($list) {
    if (!subjects || subjects.length === 0) {
        $list.html('<div class="uf-subject-no-results">暂无科目数据</div>');
        return;
    }

    // 添加搜索提示
    $list.html('<div class="uf-subject-search-hint">💡 支持科目编码、名称、拼音首字母搜索</div>');

    // 显示所有科目（最多20个）
    const displaySubjects = subjects.slice(0, 20);
    displaySubjects.forEach(subject => {
        const optionHtml = `
            <div class="uf-subject-option"
                 data-subject-id="${subject.id}"
                 data-subject-code="${subject.code}"
                 data-subject-name="${subject.name}"
                 data-pinyin="${subject.pinyin || ''}">
                <span class="uf-subject-option-code">${subject.code}</span>
                <span class="uf-subject-option-name">${subject.name}</span>
            </div>
        `;
        $list.append(optionHtml);
    });

    if (subjects.length > 20) {
        $list.append('<div class="uf-subject-more-hint">还有更多科目，请输入关键字搜索...</div>');
    }
}

// 构建过滤后的科目列表
function buildFilteredSubjectList($list, filteredSubjects, searchText) {
    $list.empty();

    if (filteredSubjects.length === 0) {
        $list.html('<div class="uf-subject-no-results">未找到匹配的科目</div>');
        return;
    }

    // 限制显示数量
    const displaySubjects = filteredSubjects.slice(0, 10);
    
    displaySubjects.forEach((subject, index) => {
        const optionHtml = `
            <div class="uf-subject-option ${index === 0 ? 'selected' : ''}"
                 data-subject-id="${subject.id}"
                 data-subject-code="${subject.code}"
                 data-subject-name="${subject.name}"
                 data-pinyin="${subject.pinyin || ''}">
                <span class="uf-subject-option-code">${highlightMatch(subject.code, searchText)}</span>
                <span class="uf-subject-option-name">${highlightMatch(subject.name, searchText)}</span>
                ${subject.pinyin ? `<span class="uf-subject-option-pinyin">${highlightMatch(subject.pinyin.toUpperCase(), searchText.toUpperCase())}</span>` : ''}
            </div>
        `;
        $list.append(optionHtml);
    });

    if (filteredSubjects.length > 10) {
        $list.append(`<div class="uf-subject-more-hint">还有 ${filteredSubjects.length - 10} 个匹配项，请继续输入缩小范围...</div>`);
    }
}

// 高亮匹配文本
function highlightMatch(text, searchText) {
    if (!searchText || !text) return text;
    
    const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// 转义正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 选择科目
function selectSubject($option) {
    const subjectId = $option.data('subject-id');
    const subjectCode = $option.data('subject-code');
    const subjectName = $option.data('subject-name');
    const subjectPinyin = $option.data('pinyin');

    const $container = $option.closest('.uf-subject-selector');
    const $input = $container.find('.uf-subject-input');
    const $dropdown = $container.find('.uf-subject-dropdown');

    // 设置输入框值
    $input.val(`${subjectCode} ${subjectName}`);
    $input.data('subject-id', subjectId);
    $input.data('subject-code', subjectCode);
    $input.data('subject-name', subjectName);

    // 隐藏下拉框
    $dropdown.hide();

    // 移动到下一个输入框（借方金额）
    const $row = $container.closest('tr');
    const $debitInput = $row.find('.uf-debit-amount');
    setTimeout(() => {
        $debitInput.focus().select();
    }, 100);

    console.log(`✅ 选择科目: ${subjectCode} ${subjectName} (拼音: ${subjectPinyin})`);
    
    // 触发科目选择事件
    $input.trigger('subject:selected', {
        id: subjectId,
        code: subjectCode,
        name: subjectName,
        pinyin: subjectPinyin
    });
}

// 打开科目选择模态框（备用方案）
function openSubjectModal($input) {
    // 这里可以实现一个更复杂的科目选择模态框
    console.log('打开科目选择模态框');
}

// 关闭科目选择模态框
function closeSubjectModal() {
    $('.uf-subject-modal').hide();
}

// 科目搜索状态更新
function updateSubjectStatusBar() {
    const totalCount = subjects.length;
    const filteredCount = filteredSubjects.length;
    
    $('.uf-subject-status').text(
        filteredCount < totalCount 
            ? `显示 ${filteredCount} / ${totalCount} 个科目`
            : `共 ${totalCount} 个科目`
    );
}

// 科目数据验证
function validateSubjectData(subject) {
    return subject && 
           subject.id && 
           subject.code && 
           subject.name && 
           subject.subject_type;
}

// 获取科目的显示文本
function getSubjectDisplayText(subject) {
    if (!subject) return '';
    return `${subject.code} ${subject.name}`;
}

// 根据ID查找科目
function findSubjectById(id) {
    return subjects.find(subject => subject.id == id);
}

// 根据编码查找科目
function findSubjectByCode(code) {
    return subjects.find(subject => subject.code === code);
}

// 根据名称查找科目
function findSubjectByName(name) {
    return subjects.find(subject => subject.name === name);
}

// 将主要函数暴露到全局作用域
window.initSubjectSelector = initSubjectSelector;
window.loadSubjects = loadSubjects;
window.buildUFSubjectTable = buildUFSubjectTable;
window.buildSubjectTree = buildSubjectTree;
window.renderSubjectTree = renderSubjectTree;
window.toggleTreeNode = toggleTreeNode;
window.selectTreeSubject = selectTreeSubject;
window.confirmSubjectSelection = confirmSubjectSelection;
window.searchSubjects = searchSubjects;
window.searchUFSubjects = searchUFSubjects;
window.expandAllSubjects = expandAllSubjects;
window.collapseAllSubjects = collapseAllSubjects;
window.openUFSubjectModal = openUFSubjectModal;
window.closeUFSubjectModal = closeUFSubjectModal;
window.showSubjectDropdown = showSubjectDropdown;
window.buildSubjectOptions = buildSubjectOptions;
window.filterSubjects = filterSubjects;
window.handleSubjectKeydown = handleSubjectKeydown;
window.navigateOptions = navigateOptions;
window.selectSubject = selectSubject;
window.findSubjectById = findSubjectById;
window.findSubjectByCode = findSubjectByCode;
window.findSubjectByName = findSubjectByName;
