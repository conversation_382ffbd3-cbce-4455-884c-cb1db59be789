/**
 * 拼音首字母助手
 * 用于会计科目快速输入的拼音首字母匹配
 */

// 完整的中文拼音首字母映射表
const PINYIN_MAP = {
    // 基础汉字拼音首字母
    '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
    '零': 'L', '百': 'B', '千': 'Q', '万': 'W', '亿': 'Y',
    
    // 常用会计科目汉字
    '现': 'X', '金': 'J', '银': 'Y', '行': 'H', '存': 'C', '款': 'K',
    '应': 'Y', '收': 'S', '账': 'Z', '库': 'K', '商': 'S', '品': 'P',
    '固': 'G', '定': 'D', '资': 'Z', '产': 'C', '付': 'F', '短': 'D',
    '期': 'Q', '借': 'J', '实': 'S', '本': 'B', '主': 'Z', '营': 'Y',
    '业': 'Y', '务': 'W', '入': 'R', '成': 'C', '管': 'G', '理': 'L',
    '费': 'F', '用': 'Y', '财': 'C', '销': 'X', '售': 'S', '制': 'Z',
    '造': 'Z', '研': 'Y', '发': 'F', '投': 'T', '长': 'C', '预': 'Y',
    '其': 'Q', '他': 'T', '货': 'H', '币': 'B', '票': 'P', '据': 'J',
    '原': 'Y', '材': 'C', '料': 'L', '低': 'D', '值': 'Z', '易': 'Y',
    '耗': 'H', '包': 'B', '装': 'Z', '物': 'W', '委': 'W', '托': 'T',
    '加': 'J', '工': 'G', '分': 'F', '期': 'Q', '摊': 'T', '待': 'D',
    '处': 'C', '累': 'L', '计': 'J', '折': 'Z', '旧': 'J', '减': 'J',
    '值': 'Z', '准': 'Z', '备': 'B', '在': 'Z', '建': 'J', '程': 'C',
    '无': 'W', '形': 'X', '开': 'K', '办': 'B', '递': 'D', '延': 'Y',
    '所': 'S', '得': 'D', '税': 'S', '负': 'F', '债': 'Z', '专': 'Z',
    '项': 'X', '盈': 'Y', '余': 'Y', '公': 'G', '积': 'J', '未': 'W',
    '分': 'F', '配': 'P', '利': 'L', '润': 'R', '股': 'G', '东': 'D',
    '权': 'Q', '益': 'Y', '资': 'Z', '本': 'B', '公': 'G', '积': 'J',
    '盈': 'Y', '余': 'Y', '公': 'G', '积': 'J', '一': 'Y', '般': 'B',
    '风': 'F', '险': 'X', '专': 'Z', '用': 'Y', '基': 'J', '金': 'J',
    
    // 收入类科目
    '收': 'S', '入': 'R', '营': 'Y', '业': 'Y', '外': 'W', '投': 'T',
    '资': 'Z', '收': 'S', '益': 'Y', '公': 'G', '允': 'Y', '价': 'J',
    '变': 'B', '动': 'D', '损': 'S', '失': 'S', '汇': 'H', '兑': 'D',
    
    // 费用类科目
    '费': 'F', '用': 'Y', '管': 'G', '理': 'L', '销': 'X', '售': 'S',
    '财': 'C', '务': 'W', '营': 'Y', '业': 'Y', '税': 'S', '金': 'J',
    '及': 'J', '附': 'F', '加': 'J', '所': 'S', '得': 'D', '税': 'S',
    
    // 成本类科目
    '成': 'C', '本': 'B', '生': 'S', '产': 'C', '制': 'Z', '造': 'Z',
    '劳': 'L', '务': 'W', '机': 'J', '械': 'X', '作': 'Z', '业': 'Y',
    
    // 损益类科目
    '损': 'S', '益': 'Y', '营': 'Y', '业': 'Y', '利': 'L', '润': 'R',
    '年': 'N', '度': 'D', '净': 'J', '利': 'L', '润': 'R',
    
    // 食堂相关汉字
    '食': 'S', '堂': 'T', '餐': 'C', '厨': 'C', '饮': 'Y', '具': 'J',
    '房': 'F', '设': 'S', '备': 'B', '家': 'J', '学': 'X', '生': 'S',
    '教': 'J', '职': 'Z', '工': 'G', '员': 'Y', '伙': 'H', '补': 'B',
    '助': 'Z', '基': 'J', '金': 'J', '米': 'M', '面': 'M', '粮': 'L',
    '油': 'Y', '蔬': 'S', '菜': 'C', '肉': 'R', '调': 'T', '味': 'W',
    '清': 'Q', '洁': 'J', '包': 'B', '装': 'Z', '水': 'S', '电': 'D',
    '燃': 'R', '气': 'Q', '维': 'W', '修': 'X', '摊': 'T', '销': 'X',
    '押': 'Y', '捐': 'J', '赠': 'Z', '废': 'F', '旧': 'J', '置': 'Z',
    '附': 'F', '属': 'S', '上': 'S', '缴': 'J', '级': 'J', '贴': 'T',
    '购': 'G', '置': 'Z', '预': 'Y', '算': 'S', '支': 'Z', '出': 'C',

    // 其他常用汉字
    '总': 'Z', '部': 'B', '分': 'F', '公': 'G', '司': 'S', '企': 'Q',
    '业': 'Y', '单': 'D', '位': 'W', '个': 'G', '人': 'R', '集': 'J',
    '团': 'T', '有': 'Y', '限': 'X', '责': 'Z', '任': 'R', '股': 'G',
    '份': 'F', '合': 'H', '伙': 'H', '独': 'D', '资': 'Z', '外': 'W',
    '商': 'S', '投': 'T', '资': 'Z', '国': 'G', '有': 'Y', '民': 'M',
    '营': 'Y', '私': 'S', '人': 'R', '个': 'G', '体': 'T', '户': 'H',
    
    // 数字和单位
    '元': 'Y', '角': 'J', '分': 'F', '厘': 'L', '毫': 'H', '丝': 'S',
    '忽': 'H', '微': 'W', '纳': 'N', '皮': 'P', '飞': 'F', '阿': 'A',
    '仄': 'Z', '幺': 'Y', '么': 'M'
};

// 会计科目专用拼音映射（完整科目名称）
const ACCOUNTING_SUBJECT_PINYIN = {
    // 资产类
    '库存现金': 'KCXJ',
    '银行存款': 'YHCK',
    '其他货币资金': 'QTHBZJ',
    '应收票据': 'YSPJ',
    '应收账款': 'YSZK',
    '其他应收款': 'QTYSZK',
    '预付账款': 'YFZK',
    '应收股利': 'YSGL',
    '应收利息': 'YSLX',
    '坏账准备': 'HZZB',

    // 库存物品
    '库存物品': 'KCWP',
    '原材料': 'YCL',
    '米面粮油': 'MMLY',
    '蔬菜类': 'SCL',
    '肉类': 'RL',
    '调味品': 'TWP',
    '库存商品': 'KCSP',
    '发出商品': 'FCSP',
    '委托加工物资': 'WTJGWZ',
    '周转材料': 'ZZCL',
    '材料采购': 'CLCG',
    '在途物资': 'ZTWZ',
    '材料成本差异': 'CLCBCY',
    '低值易耗品': 'DZYH',
    '餐具': 'CJ',
    '清洁用品': 'QJYP',
    '包装材料': 'BZCL',
    '物料用品': 'WLYP',
    '待摊费用': 'DTFY',

    // 固定资产
    '固定资产': 'GDZC',
    '厨房设备': 'CFSB',
    '餐饮家具': 'CYJJ',
    '其他设备': 'QTSB',
    '固定资产累计折旧': 'GDZCLJZJ',
    '累计折旧': 'LJZJ',
    '在建工程': 'ZJGC',
    '工程物资': 'GCWZ',
    '无形资产': 'WXZC',
    '累计摊销': 'LJTX',
    '固定资产减值准备': 'GDZCJZZB',
    '无形资产减值准备': 'WXZCJZZB',
    '长期待摊费用': 'CQDTFY',
    '递延所得税资产': 'DYSDSZZC',
    
    // 负债类
    '短期借款': 'DQJK',
    '应付票据': 'YFPJ',
    '应付账款': 'YFZK',
    '预收账款': 'YSZK',
    '应付职工薪酬': 'YFZGXC',
    '其他应付款': 'QTYFK',
    '学生餐费预收余额': 'XSCFYSYE',
    '押金': 'YJ',
    '应付水电费': 'YFSDF',
    '应交税费': 'YJSF',
    '应付利息': 'YFLX',
    '应付股利': 'YFGL',
    '长期借款': 'CQJK',
    '应付债券': 'YFZQ',
    '长期应付款': 'CQYFK',
    '递延所得税负债': 'DYSDSFFZ',
    
    // 所有者权益类/净资产类
    '实收资本': 'SSZB',
    '资本公积': 'ZBGJ',
    '盈余公积': 'YYGJ',
    '本年利润': 'BNLR',
    '利润分配': 'LRFP',
    '未分配利润': 'WFPLR',
    '累计盈余': 'LJYY',
    '专用基金': 'ZYJJ',
    '学生伙食补助基金': 'XSHSBZJJ',
    
    // 收入类
    '事业收入': 'SYSR',
    '食堂收入': 'STSR',
    '学生餐费收入': 'XSCFSR',
    '教职工餐费收入': 'JZGCFSR',
    '其他经营收入': 'QTJYSR',
    '主营业务收入': 'ZYYWSR',
    '其他业务收入': 'QTYWSR',
    '上级补助收入': 'SJBZSR',
    '设备购置补贴': 'SBGZBT',
    '学生伙食补助': 'XSHSBZ',
    '附属单位上缴收入': 'FSDWSJSR',
    '其他收入': 'QTSR',
    '废旧物资处置收入': 'FJWZCZSR',
    '捐赠收入': 'JZSR',
    '营业外收入': 'YYWSR',
    '投资收益': 'TZSY',
    '公允价值变动损益': 'GYJZBYDSYY',
    
    // 费用类
    '业务活动费用': 'YWHDFY',
    '原材料成本': 'YCLCB',
    '米面粮油成本': 'MMLYCB',
    '蔬菜成本': 'SCCB',
    '肉类成本': 'RLCB',
    '调味品成本': 'TWPCB',
    '人工成本': 'RGCB',
    '水电燃气费': 'SDRGF',
    '固定资产折旧费': 'GDZCJF',
    '低值易耗品摊销': 'DZYHPTX',
    '维修费': 'WXF',
    '其他商品和服务费用': 'QTSPHHWFY',
    '分摊费用': 'FTFY',
    '主营业务成本': 'ZYYWCB',
    '其他业务成本': 'QTYWCB',
    '营业税金及附加': 'YYSJJFJ',
    '销售费用': 'XSFY',
    '管理费用': 'GLFY',
    '财务费用': 'CWFY',
    '资产减值损失': 'ZCJZSS',
    '营业外支出': 'YYWZC',
    '所得税费用': 'SDSFY',
    
    // 成本类
    '生产成本': 'SCCB',
    '制造费用': 'ZZFY',
    '劳务成本': 'LWCB',

    // 预算会计科目
    '预算收入': 'YSSR',
    '食堂预算收入': 'STYSSR',
    '预算支出': 'YSZC',
    '食堂预算支出': 'STYSZC'
};

/**
 * 获取单个汉字的拼音首字母
 * @param {string} char - 单个汉字
 * @returns {string} 拼音首字母
 */
function getCharPinyin(char) {
    return PINYIN_MAP[char] || char.toUpperCase();
}

/**
 * 获取会计科目的拼音首字母
 * @param {string} subjectName - 科目名称
 * @returns {string} 拼音首字母组合
 */
function getSubjectPinyin(subjectName) {
    if (!subjectName) return '';
    
    // 首先检查是否有完整的科目名称映射
    if (ACCOUNTING_SUBJECT_PINYIN[subjectName]) {
        return ACCOUNTING_SUBJECT_PINYIN[subjectName].toLowerCase();
    }
    
    // 逐字符转换拼音首字母
    let pinyin = '';
    for (let i = 0; i < subjectName.length; i++) {
        const char = subjectName[i];
        if (PINYIN_MAP[char]) {
            pinyin += PINYIN_MAP[char];
        } else if (/[a-zA-Z]/.test(char)) {
            pinyin += char.toUpperCase();
        } else if (/\d/.test(char)) {
            pinyin += char;
        }
        // 跳过标点符号和其他字符
    }
    
    return pinyin.toLowerCase();
}

/**
 * 检查输入是否匹配科目
 * @param {string} input - 用户输入
 * @param {Object} subject - 科目对象 {code, name, pinyin}
 * @returns {boolean} 是否匹配
 */
function matchSubject(input, subject) {
    if (!input || !subject) return false;
    
    const searchTerm = input.toLowerCase().trim();
    
    // 匹配科目编码
    if (subject.code && subject.code.toLowerCase().includes(searchTerm)) {
        return true;
    }
    
    // 匹配科目名称
    if (subject.name && subject.name.toLowerCase().includes(searchTerm)) {
        return true;
    }
    
    // 匹配拼音首字母
    if (subject.pinyin && subject.pinyin.toLowerCase().includes(searchTerm)) {
        return true;
    }
    
    // 匹配拼音首字母的部分匹配（支持连续输入）
    if (subject.pinyin) {
        const pinyin = subject.pinyin.toLowerCase();
        // 支持连续字母匹配，如输入"xj"匹配"现金"的"xianjin"
        if (pinyin.startsWith(searchTerm)) {
            return true;
        }
    }
    
    return false;
}

/**
 * 为科目数组添加拼音首字母
 * @param {Array} subjects - 科目数组
 * @returns {Array} 添加了拼音字段的科目数组
 */
function addPinyinToSubjects(subjects) {
    return subjects.map(subject => ({
        ...subject,
        pinyin: getSubjectPinyin(subject.name)
    }));
}

/**
 * 过滤匹配的科目
 * @param {Array} subjects - 科目数组
 * @param {string} searchTerm - 搜索词
 * @returns {Array} 匹配的科目数组
 */
function filterSubjects(subjects, searchTerm) {
    if (!searchTerm) return subjects;
    
    return subjects.filter(subject => matchSubject(searchTerm, subject));
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getSubjectPinyin,
        getCharPinyin,
        matchSubject,
        addPinyinToSubjects,
        filterSubjects,
        PINYIN_MAP,
        ACCOUNTING_SUBJECT_PINYIN
    };
}
