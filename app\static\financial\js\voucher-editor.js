/**
 * 财务凭证编辑器
 * 用友风格的凭证录入界面
 */

// 全局变量
let subjects = [];
let filteredSubjects = [];
let voucherMode = 'create'; // create, edit, view
let currentVoucherId = null;

// 初始化凭证编辑器
function initVoucherEditor(mode = 'create', voucherId = null) {
    // 标记已初始化，避免重复初始化
    window.voucherEditorInitialized = true;

    voucherMode = mode;
    currentVoucherId = voucherId;

    console.log(`🚀 初始化凭证编辑器 - 模式: ${mode}, ID: ${voucherId}`);
    
    // 加载会计科目数据
    loadAccountingSubjects();
    
    // 初始化表格
    initVoucherTable();
    
    // 初始化事件监听
    initEventListeners();
    
    // 初始化快捷键
    initUFKeyboardShortcuts();
    
    // 初始化摘要自动调整
    initSummaryAutoResize();
    
    // 优化表格布局
    optimizeTableLayout();
    
    // 显示欢迎消息
    if (mode === 'create') {
        showUFMessage('欢迎使用用友风格记账凭证编辑器！按F1查看快捷键帮助', 'info', 5000);
    }
}

// 加载会计科目数据
function loadAccountingSubjects() {
    console.log('📋 开始加载会计科目数据...');
    
    fetch('/financial/api/accounting-subjects')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(response => {
            // 适配现有API的返回格式（直接返回数组）
            if (Array.isArray(response) && response.length > 0) {
                subjects = response;
                console.log(`✅ 科目数据加载成功: ${subjects.length} 个科目`);

                // 为所有科目添加拼音首字母
                subjects = addPinyinToSubjects(subjects);
                
                // 按科目类型分组显示统计
                const typeStats = {};
                subjects.forEach(subject => {
                    const type = subject.subject_type || '其他';
                    typeStats[type] = (typeStats[type] || 0) + 1;
                });
                console.log('科目类型统计:', typeStats);

                // 显示部分科目数据用于调试
                if (subjects.length > 0) {
                    console.log('科目数据示例:', subjects.slice(0, 3));
                }

                // 初始化科目选择器
                initSubjectSelectors();
            } else {
                console.warn('⚠️ 科目数据格式异常或为空:', response);
                showUFMessage('科目数据加载异常，请刷新页面重试', 'warning');
            }
        })
        .catch(error => {
            console.error('❌ 科目数据加载失败:', error);
            showUFMessage('科目数据加载失败，请检查网络连接', 'error');
            
            // 使用备用数据
            subjects = getDefaultSubjects();
            subjects = addPinyinToSubjects(subjects);
            initSubjectSelectors();
        });
}

// 获取默认科目数据（备用）
function getDefaultSubjects() {
    return [
        {id: 1, code: '1001', name: '库存现金', subject_type: '资产', balance_direction: '借方'},
        {id: 2, code: '1002', name: '银行存款', subject_type: '资产', balance_direction: '借方'},
        {id: 3, code: '1121', name: '应收账款', subject_type: '资产', balance_direction: '借方'},
        {id: 4, code: '1401', name: '库存商品', subject_type: '资产', balance_direction: '借方'},
        {id: 5, code: '2001', name: '应付账款', subject_type: '负债', balance_direction: '贷方'},
        {id: 6, code: '2002', name: '短期借款', subject_type: '负债', balance_direction: '贷方'},
        {id: 7, code: '3001', name: '实收资本', subject_type: '所有者权益', balance_direction: '贷方'},
        {id: 8, code: '6001', name: '主营业务收入', subject_type: '收入', balance_direction: '贷方'},
        {id: 9, code: '6401', name: '主营业务成本', subject_type: '费用', balance_direction: '借方'},
        {id: 10, code: '6602', name: '销售费用', subject_type: '费用', balance_direction: '借方'},
        {id: 11, code: '6603', name: '管理费用', subject_type: '费用', balance_direction: '借方'},
        {id: 12, code: '6604', name: '财务费用', subject_type: '费用', balance_direction: '借方'}
    ];
}

// 初始化凭证表格
function initVoucherTable() {
    console.log('📊 初始化凭证表格...');
    
    // 确保至少有两行
    const $tbody = $('.uf-voucher-table tbody');
    const currentRows = $tbody.find('tr').length;
    
    if (currentRows < 2) {
        for (let i = currentRows; i < 2; i++) {
            addUFRow();
        }
    }
    
    // 更新行号
    updateUFLineNumbers();
    
    // 初始化金额格式化
    initAmountFormatting();
    
    console.log('✅ 凭证表格初始化完成');
}

// 初始化事件监听
function initEventListeners() {
    console.log('🎧 初始化事件监听...');
    
    // 科目输入框事件
    $(document).on('input', '.uf-subject-input', handleSubjectInput);
    $(document).on('focus', '.uf-subject-input', handleSubjectFocus);
    $(document).on('blur', '.uf-subject-input', handleSubjectBlur);
    $(document).on('keydown', '.uf-subject-input', handleSubjectKeydown);
    
    // 金额输入框事件
    $(document).on('focus', '.uf-debit-amount, .uf-credit-amount', function() {
        prepareUFAmountEdit(this);
    });
    $(document).on('input', '.uf-debit-amount, .uf-credit-amount', function() {
        formatUFAmountInput(this);
        // 实时更新合计
        updateTotals();
    });
    $(document).on('blur', '.uf-debit-amount, .uf-credit-amount', function() {
        finalizeUFAmountFormat(this);
        updateTotals();
    });
    
    // 摘要输入框事件
    $(document).on('input', '.uf-summary-input', function() {
        autoResizeTextarea(this);
    });
    
    // 添加行按钮
    $(document).on('click', '.uf-add-row', addUFRow);
    
    // 删除行按钮
    $(document).on('click', '.uf-delete-row', function() {
        deleteUFRow($(this).closest('tr'));
    });
    
    // 科目选择事件
    $(document).on('click', '.uf-subject-option', function() {
        selectSubject($(this));
    });
    
    // 窗口大小改变事件
    let resizeTimer;
    $(window).on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            optimizeTableLayout();
            $('.uf-summary-input').each(function() {
                autoResizeTextarea(this);
            });
        }, 150);
    });
    
    // 页面离开确认
    window.addEventListener('beforeunload', function(e) {
        if (voucherMode !== 'view' && hasUnsavedChanges()) {
            e.preventDefault();
            e.returnValue = '您有未保存的修改，确定要离开吗？';
            return e.returnValue;
        }
    });
    
    console.log('✅ 事件监听初始化完成');
}

// 初始化科目选择器
function initSubjectSelectors() {
    console.log('🔍 初始化科目选择器...');
    
    // 为每个科目输入框初始化
    $('.uf-subject-input').each(function() {
        const $input = $(this);
        const $container = $input.closest('.uf-subject-selector');
        
        // 确保有下拉框
        if ($container.find('.uf-subject-dropdown').length === 0) {
            $container.append(`
                <div class="uf-subject-dropdown" style="display: none;">
                    <div class="uf-subject-dropdown-list">
                        <!-- 科目选项将通过JavaScript动态生成 -->
                    </div>
                </div>
            `);
        }
    });
    
    console.log('✅ 科目选择器初始化完成');
}

// 初始化金额格式化
function initAmountFormatting() {
    console.log('💰 初始化金额格式化...');
    
    $('.uf-debit-amount, .uf-credit-amount').each(function() {
        const $input = $(this);
        const value = $input.val();
        
        if (value) {
            finalizeUFAmountFormat(this);
        }
    });
    
    // 更新合计
    updateTotals();
    
    console.log('✅ 金额格式化初始化完成');
}

// 更新行号
function updateUFLineNumbers() {
    $('.uf-voucher-table tbody tr').each(function(index) {
        $(this).find('.uf-line-number').text(index + 1);
    });
}

// 更新合计
function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;
    
    $('.uf-debit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        if (value > 0) debitTotal += value;
    });
    
    $('.uf-credit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        if (value > 0) creditTotal += value;
    });
    
    // 更新合计显示
    $('#debit-total').text(formatUFAmountDisplay(debitTotal));
    $('#credit-total').text(formatUFAmountDisplay(creditTotal));
    
    // 更新平衡状态
    const isBalanced = Math.abs(debitTotal - creditTotal) < 0.01;
    $('#balance-status')
        .removeClass('balanced unbalanced')
        .addClass(isBalanced ? 'balanced' : 'unbalanced')
        .text(isBalanced ? '平衡' : '不平衡');
    
    // 更新大写金额
    if (isBalanced && debitTotal > 0) {
        $('.uf-amount-chinese').text(convertAmountToChinese(debitTotal));
    } else {
        $('.uf-amount-chinese').text('');
    }
}

// 检查是否有未保存的修改
function hasUnsavedChanges() {
    // 简单检查：比较当前表单数据与初始数据
    // 这里可以根据实际需求实现更复杂的检查逻辑
    return false; // 暂时返回false，后续可以完善
}

// 将主要函数暴露到全局作用域，供HTML页面调用
window.initVoucherEditor = initVoucherEditor;
window.updateUFTotals = updateTotals;
window.checkUFBalance = checkBalance;
window.saveUFVoucher = saveVoucher;
window.collectVoucherData = collectVoucherData;
window.validateVoucherData = validateVoucherData;

// 自动初始化（如果页面没有手动调用）
$(document).ready(function() {
    // 延迟一点时间，让HTML页面有机会手动调用
    setTimeout(function() {
        if (!window.voucherEditorInitialized) {
            console.log('🔄 自动初始化凭证编辑器...');
            // 从页面获取模式和ID
            const mode = window.voucherMode || 'create';
            const voucherId = window.voucherId || null;

            // 初始化编辑器
            initVoucherEditor(mode, voucherId);
        }
    }, 200);
});
