/**
 * 用友风格金额格式化工具
 * 支持千分位分隔符、中文大写转换等
 */

// 准备金额编辑（聚焦时）
function prepareUFAmountEdit(input) {
    const $input = $(input);
    const value = $input.val();

    if (value) {
        // 移除格式化，显示纯数字便于编辑
        const numericValue = parseUFAmount(value);
        if (numericValue > 0) {
            $input.val(numericValue.toString());
        }
    }

    // 隐藏单位
    $input.siblings('.uf-amount-unit').hide();
}

// 实时金额格式化（输入时）
function formatUFAmountInput(input) {
    const $input = $(input);
    let value = $input.val();

    // 只允许数字、小数点和负号
    value = value.replace(/[^\d.-]/g, '');

    // 确保只有一个小数点
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数位数为2位
    if (parts.length === 2 && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }

    $input.val(value);

    // 更新样式状态
    updateAmountInputState($input, value);
}

// 完成金额格式化（失焦时）
function finalizeUFAmountFormat(input) {
    const $input = $(input);
    const value = $input.val().trim();

    if (!value || value === '0' || value === '0.00') {
        // 空值或零值
        $input.val('');
        $input.removeClass('has-value zero invalid large-amount');
        $input.siblings('.uf-amount-unit').show();
        return;
    }

    const numericValue = parseFloat(value);

    if (isNaN(numericValue) || numericValue < 0) {
        // 无效值
        $input.addClass('invalid');
        $input.siblings('.uf-amount-unit').show();
        return;
    }

    // 格式化显示
    const formattedValue = formatUFAmountDisplay(numericValue);
    $input.val(formattedValue);

    // 更新样式状态
    updateAmountInputState($input, numericValue);

    // 显示单位
    $input.siblings('.uf-amount-unit').show();

    console.log(`💰 金额格式化完成: ${value} -> ${formattedValue}`);
}

// 格式化金额显示
function formatUFAmountDisplay(amount) {
    if (!amount || amount === 0) return '';

    // 用友风格：千分位分隔符 + 两位小数
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
        useGrouping: true
    });
}

// 解析金额字符串为数字
function parseUFAmount(amountStr) {
    if (!amountStr) return 0;

    // 移除千分位分隔符、货币符号等
    const cleanStr = amountStr.toString()
        .replace(/[¥￥$€£,\s]/g, '')
        .replace(/[，]/g, '');

    const amount = parseFloat(cleanStr);
    return isNaN(amount) ? 0 : amount;
}

// 更新金额输入框状态
function updateAmountInputState($input, value) {
    const numericValue = typeof value === 'number' ? value : parseUFAmount(value);

    // 清除所有状态类
    $input.removeClass('has-value zero invalid large-amount');

    if (numericValue > 0) {
        $input.addClass('has-value');

        // 大金额警告（超过100万）
        if (numericValue >= 1000000) {
            $input.addClass('large-amount');
        }
    } else if (numericValue === 0) {
        $input.addClass('zero');
    } else {
        $input.addClass('invalid');
    }

    // 根据借贷方向设置颜色
    const amountType = $input.data('amount-type');
    if (numericValue > 0) {
        if (amountType === 'debit') {
            $input.addClass('has-value');
        } else if (amountType === 'credit') {
            $input.addClass('has-value');
        }
    }
}

// 验证金额输入
function validateUFAmount(input) {
    const $input = $(input);
    const value = $input.val();
    const numericValue = parseUFAmount(value);

    // 检查是否为有效数字
    if (value && (isNaN(numericValue) || numericValue < 0)) {
        $input.addClass('invalid');
        return false;
    }

    $input.removeClass('invalid');
    return true;
}

// 获取格式化的金额文本（用于显示）
function getFormattedAmountText(amount) {
    if (!amount || amount === 0) return '';
    return formatUFAmountDisplay(amount);
}

// 金额转换为大写（中文）
function convertAmountToChinese(amount) {
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
    const decimals = ['角', '分'];

    if (amount === 0) return '零元整';

    let result = '';
    const amountStr = amount.toFixed(2);
    const [integerPart, decimalPart] = amountStr.split('.');

    // 处理整数部分
    for (let i = 0; i < integerPart.length; i++) {
        const digit = parseInt(integerPart[i]);
        const unitIndex = integerPart.length - i - 1;

        if (digit !== 0) {
            result += digits[digit] + units[unitIndex];
        } else if (result && !result.endsWith('零')) {
            result += '零';
        }
    }

    result += '元';

    // 处理小数部分
    if (decimalPart && decimalPart !== '00') {
        const jiao = parseInt(decimalPart[0]);
        const fen = parseInt(decimalPart[1]);

        if (jiao > 0) {
            result += digits[jiao] + '角';
        }
        if (fen > 0) {
            result += digits[fen] + '分';
        }
    } else {
        result += '整';
    }

    return result;
}

// 金额计算工具
const AmountCalculator = {
    // 加法（避免浮点数精度问题）
    add: function(a, b) {
        const factor = 100;
        return Math.round((a * factor + b * factor)) / factor;
    },

    // 减法
    subtract: function(a, b) {
        const factor = 100;
        return Math.round((a * factor - b * factor)) / factor;
    },

    // 乘法
    multiply: function(a, b) {
        const factor = 100;
        return Math.round(a * b * factor) / factor;
    },

    // 除法
    divide: function(a, b) {
        if (b === 0) return 0;
        return Math.round((a / b) * 100) / 100;
    },

    // 比较是否相等（考虑精度）
    equals: function(a, b, precision = 0.01) {
        return Math.abs(a - b) < precision;
    },

    // 格式化为会计格式
    format: function(amount) {
        return formatUFAmountDisplay(amount);
    },

    // 解析金额
    parse: function(amountStr) {
        return parseUFAmount(amountStr);
    }
};

// 金额输入框增强功能
function enhanceAmountInput($input) {
    // 添加右键菜单
    $input.on('contextmenu', function(e) {
        e.preventDefault();
        showAmountContextMenu(e, $input);
    });

    // 添加双击清空功能
    $input.on('dblclick', function() {
        $(this).val('').trigger('blur');
    });

    // 添加Ctrl+A全选功能
    $input.on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            this.select();
        }
    });
}

// 显示金额右键菜单
function showAmountContextMenu(e, $input) {
    const menuHtml = `
        <div class="uf-amount-context-menu" style="
            position: fixed;
            left: ${e.pageX}px;
            top: ${e.pageY}px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 10000;
            min-width: 120px;
        ">
            <div class="menu-item" data-action="clear">清空</div>
            <div class="menu-item" data-action="copy">复制</div>
            <div class="menu-item" data-action="paste">粘贴</div>
            <div class="menu-separator"></div>
            <div class="menu-item" data-action="chinese">转大写</div>
        </div>
    `;

    // 移除现有菜单
    $('.uf-amount-context-menu').remove();

    // 添加新菜单
    $('body').append(menuHtml);

    // 菜单项点击事件
    $('.uf-amount-context-menu .menu-item').on('click', function() {
        const action = $(this).data('action');
        handleAmountMenuAction(action, $input);
        $('.uf-amount-context-menu').remove();
    });

    // 点击其他地方关闭菜单
    $(document).one('click', function() {
        $('.uf-amount-context-menu').remove();
    });
}

// 处理金额菜单操作
function handleAmountMenuAction(action, $input) {
    const value = $input.val();
    const numericValue = parseUFAmount(value);

    switch (action) {
        case 'clear':
            $input.val('').trigger('blur');
            break;
        case 'copy':
            navigator.clipboard.writeText(value);
            break;
        case 'paste':
            navigator.clipboard.readText().then(text => {
                $input.val(text).trigger('input').trigger('blur');
            });
            break;
        case 'chinese':
            if (numericValue > 0) {
                const chinese = convertAmountToChinese(numericValue);
                showUFMessage(`大写金额：${chinese}`, 'info', 5000);
            }
            break;
    }
}

// 将主要函数暴露到全局作用域
window.prepareUFAmountEdit = prepareUFAmountEdit;
window.formatUFAmountInput = formatUFAmountInput;
window.finalizeUFAmountFormat = finalizeUFAmountFormat;
window.formatUFAmountDisplay = formatUFAmountDisplay;
window.parseUFAmount = parseUFAmount;
window.updateAmountInputState = updateAmountInputState;
window.validateUFAmount = validateUFAmount;
window.getFormattedAmountText = getFormattedAmountText;
window.convertAmountToChinese = convertAmountToChinese;
window.AmountCalculator = AmountCalculator;
window.enhanceAmountInput = enhanceAmountInput;
