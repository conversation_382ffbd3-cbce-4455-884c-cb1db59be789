{% extends 'financial/base.html' %}

{% block title %}
{% if mode == 'view' %}
查看记账凭证 - {{ super() }}
{% elif mode == 'edit' %}
编辑记账凭证 - {{ super() }}
{% else %}
新建记账凭证 - {{ super() }}
{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<!-- 重新载入用友主题样式，确保优先级 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}?v=2.1.0">
<!-- 载入凭证专用样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/voucher-style.css') }}?v=1.0.0">

<!-- 用友风格增强样式 -->
<style>
/* 消息提示动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 按钮悬停效果 */
.uf-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

/* 输入框聚焦效果 */
.uf-form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    transition: all 0.2s ease;
}

/* 表格行悬停效果 */
.uf-voucher-row:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* 科目选择器动画 */
.uf-subject-dropdown {
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.uf-loading {
    position: relative;
    pointer-events: none;
}

.uf-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

{% endblock %}

{% block page_title %}
{% if mode == 'view' %}
查看记账凭证
{% elif mode == 'edit' %}
编辑记账凭证
{% else %}
新建记账凭证
{% endif %}
{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">
{% if mode == 'view' %}
查看凭证
{% elif mode == 'edit' %}
编辑凭证
{% else %}
新增凭证
{% endif %}
</span>
{% endblock %}

{% block financial_content %}
<!-- 用友风格记账凭证编辑器 -->
<div class="uf-voucher-editor">
    <!-- 凭证窗口 -->
    <div class="uf-voucher-window">
        <!-- 窗口标题栏 -->
        <div class="uf-window-header">
            <div class="uf-window-title">
                <i class="uf-icon">📋</i>
                <span>记账凭证</span>
                {% if voucher %}
                    <span class="uf-status-badge uf-status-{{ voucher.status|lower }}">{{ voucher.status }}</span>
                {% endif %}
            </div>
            <div class="uf-window-controls">
                {% if mode == 'view' %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.edit_voucher_unified', id=voucher.id) }}'">
                    <i class="uf-icon">✏️</i> 编辑
                </button>
                {% endif %}
                {% if mode == 'edit' %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.view_voucher', id=voucher.id) }}'">
                    <i class="uf-icon">👁️</i> 查看
                </button>
                {% endif %}
                <button class="uf-btn uf-btn-sm" onclick="location.href='{{ url_for('financial.vouchers_index') }}'">
                    <i class="uf-icon">📋</i> 列表
                </button>
                <button class="uf-btn uf-btn-sm" onclick="window.close()">
                    <i class="uf-icon">✕</i> 关闭
                </button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="uf-toolbar">
            <!-- 凭证导航组 -->
            <div class="uf-toolbar-group uf-navigation-group">
                <button class="uf-btn uf-btn-nav" onclick="navigateToFirst()" title="首张凭证">
                    <i class="uf-icon">⏮️</i> 首张
                </button>
                <button class="uf-btn uf-btn-nav" onclick="navigateToPrevious()" title="上一张凭证">
                    <i class="uf-icon">⏪</i> 上一张
                </button>
                <div class="uf-voucher-number-input">
                    <input type="text" class="uf-form-control uf-nav-input" id="nav-voucher-number"
                           placeholder="凭证号" title="输入凭证号跳转">
                    <button class="uf-btn uf-btn-nav" onclick="navigateToVoucher()" title="跳转到指定凭证">
                        <i class="uf-icon">�</i>
                    </button>
                </div>
                <button class="uf-btn uf-btn-nav" onclick="navigateToNext()" title="下一张凭证">
                    <i class="uf-icon">⏩</i> 下一张
                </button>
                <button class="uf-btn uf-btn-nav" onclick="navigateToLast()" title="末张凭证">
                    <i class="uf-icon">⏭️</i> 末张
                </button>
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 凭证操作组 -->
            <div class="uf-toolbar-group">
                <button class="uf-btn uf-btn-primary" onclick="newVoucher()" title="新增凭证">
                    <i class="uf-icon">📄</i> 新增
                </button>
                {% if mode != 'view' %}
                <button class="uf-btn uf-btn-primary" onclick="saveVoucher()">
                    <i class="uf-icon">�</i> 保存
                </button>
                {% endif %}
                {% if voucher %}
                <button class="uf-btn" onclick="copyVoucher()" title="复制凭证">
                    <i class="uf-icon">📋</i> 复制
                </button>
                <button class="uf-btn" onclick="reverseVoucher()" title="冲销凭证">
                    <i class="uf-icon">🔄</i> 冲销
                </button>
                {% endif %}
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 编辑操作组 -->
            {% if mode != 'view' %}
            <div class="uf-toolbar-group">
                <button class="uf-btn" onclick="addRow()">
                    <i class="uf-icon">➕</i> 增行
                </button>
                <button class="uf-btn" onclick="deleteRow()">
                    <i class="uf-icon">➖</i> 删行
                </button>
                <button class="uf-btn" onclick="insertRow()">
                    <i class="uf-icon">📝</i> 插行
                </button>
            </div>

            <div class="uf-toolbar-separator"></div>
            {% endif %}

            <!-- 打印和审核组 -->
            <div class="uf-toolbar-group">
                <button class="uf-btn" onclick="printVoucher()" title="打印凭证">
                    <i class="uf-icon">🖨️</i> 打印
                </button>
                <button class="uf-btn" onclick="printPreview()" title="打印预览">
                    <i class="uf-icon">👁️</i> 预览
                </button>
                <button class="uf-btn" onclick="checkBalance()">
                    <i class="uf-icon">⚖️</i> 平衡
                </button>
                {% if voucher and voucher.status == '待审核' %}
                <button class="uf-btn uf-btn-success" onclick="reviewVoucher()">
                    <i class="uf-icon">✅</i> 审核
                </button>
                {% endif %}
                {% if voucher and voucher.status == '已审核' %}
                <button class="uf-btn uf-btn-warning" onclick="unReviewVoucher()">
                    <i class="uf-icon">↩️</i> 取消审核
                </button>
                {% endif %}
            </div>

            <div class="uf-toolbar-separator"></div>

            <!-- 状态指示器 -->
            <div class="uf-balance-indicator">
                <span class="uf-balance-label">借贷平衡:</span>
                <span class="uf-balance-status" id="balance-indicator">未检查</span>
            </div>
        </div>

        <!-- 凭证信息栏 -->
        <div class="uf-voucher-info">
            <div class="uf-info-row">
                <!-- 凭证字 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">凭证字:</label>
                    <select class="uf-form-control uf-voucher-type" id="voucher-type" {% if mode == 'view' %}disabled{% endif %}>
                        <option value="记" {% if voucher and voucher.voucher_type == '记' %}selected{% endif %}>记</option>
                        <option value="收" {% if voucher and voucher.voucher_type == '收' %}selected{% endif %}>收</option>
                        <option value="付" {% if voucher and voucher.voucher_type == '付' %}selected{% endif %}>付</option>
                        <option value="转" {% if voucher and voucher.voucher_type == '转' %}selected{% endif %}>转</option>
                    </select>
                </div>

                <!-- 凭证号 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">号:</label>
                    <input type="text" class="uf-form-control uf-voucher-number" id="voucher-number"
                           value="{% if voucher %}{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}{% else %}自动{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %} placeholder="自动">
                </div>

                <!-- 日期 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">日期:</label>
                    <input type="date" class="uf-form-control uf-voucher-date" id="voucher-date"
                           value="{% if voucher %}{{ voucher.voucher_date }}{% else %}{{ today }}{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %}>
                </div>

                <!-- 附件 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">附件:</label>
                    <div class="uf-attachment-input-group">
                        <input type="number" class="uf-form-control uf-attachment-count" id="attachment-count"
                               value="{% if voucher %}{{ voucher.attachment_count or 0 }}{% else %}0{% endif %}"
                               min="0" {% if mode == 'view' %}readonly{% endif %}>
                        <span class="uf-attachment-unit">张</span>
                    </div>
                </div>

                <!-- 制单人 -->
                <div class="uf-form-group">
                    <label class="uf-form-label">制单:</label>
                    <span class="uf-form-text">
                        {% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- 凭证表格 -->
        <div class="uf-voucher-table-container">
            <table class="uf-table uf-voucher-table" id="voucher-table">
                <thead>
                    <tr>
                        <th class="uf-col-sequence">序号</th>
                        <th class="uf-col-summary">摘要</th>
                        <th class="uf-col-subject">会计科目</th>
                        <th class="uf-col-debit">借方金额</th>
                        <th class="uf-col-credit">贷方金额</th>
                            
                        {% if mode != 'view' %}
                        <th class="uf-col-operations">操作</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                    {% if voucher and details %}
                        {% for detail in details %}
                        <tr class="uf-voucher-row" data-row="{{ loop.index }}" data-detail-id="{{ detail.id }}">
                            <td class="uf-line-number">{{ loop.index }}</td>
                            <td class="uf-summary-cell">
                                <textarea class="uf-form-control uf-summary-input"
                                          {% if mode == 'view' %}readonly{% endif %}
                                          placeholder="摘要"
                                          rows="2">{{ detail.summary }}</textarea>
                            </td>
                            <td class="uf-subject-cell">
                                <div class="uf-subject-selector">
                                    <input type="text" class="uf-form-control uf-subject-code"
                                           value="{{ detail.subject.code }}"
                                           {% if mode == 'view' %}readonly{% else %}readonly onclick="openSubjectModal(this)"{% endif %}
                                           placeholder="科目编码">
                                    <input type="text" class="uf-form-control uf-subject-name"
                                           value="{{ detail.subject.name }}"
                                           readonly placeholder="科目名称">
                                    <input type="hidden" class="uf-subject-id" value="{{ detail.subject.id }}">
                                </div>
                            </td>
                            <td class="uf-amount-cell">
                                <div class="uf-amount-input-container">
                                    <input type="text" class="uf-form-control uf-amount-input uf-debit-amount"
                                           value="{% if detail.debit_amount > 0 %}{{ '{:,.2f}'.format(detail.debit_amount) }}{% endif %}"
                                           {% if mode == 'view' %}readonly{% endif %}
                                           placeholder=""
                                           data-amount-type="debit">
                                    <span class="uf-amount-unit">元</span>
                                </div>
                            </td>
                            <td class="uf-amount-cell">
                                <div class="uf-amount-input-container">
                                    <input type="text" class="uf-form-control uf-amount-input uf-credit-amount"
                                           value="{% if detail.credit_amount > 0 %}{{ '{:,.2f}'.format(detail.credit_amount) }}{% endif %}"
                                           {% if mode == 'view' %}readonly{% endif %}
                                           placeholder=""
                                           data-amount-type="credit">
                                    <span class="uf-amount-unit">元</span>
                                </div>
                            </td>
                            {% if mode != 'view' %}
                            <td class="uf-operations-cell">
                                <button class="uf-btn uf-btn-sm" onclick="insertRowAbove(this)" title="插行">
                                    <i class="uf-icon">⬆️</i>
                                </button>
                                <button class="uf-btn uf-btn-sm" onclick="deleteRow(this)" title="删行">
                                    <i class="uf-icon">🗑️</i>
                                </button>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% else %}
                        <!-- 新建模式下的空行将通过JavaScript动态生成 -->
                    {% endif %}
                </tbody>
                <tfoot>
                    <!-- 大写金额行 -->
                    <tr class="uf-chinese-amount-row">
                        <td class="uf-chinese-amount-label">大写</td>
                        <td colspan="{% if mode != 'view' %}5{% else %}4{% endif %}" class="uf-chinese-amount-cell">
                            <div class="uf-chinese-amount-container">
                                <span class="uf-chinese-prefix">人民币(大写)：</span>
                                <span id="amount-chinese" class="uf-chinese-amount-text">零元整</span>
                            </div>
                        </td>
                    </tr>
                    <!-- 合计行 -->
                    <tr class="uf-totals-row">
                        <td class="uf-totals-label">合计</td>
                        <td class="uf-totals-label"></td>
                        <td class="uf-totals-label"></td>
                        <td class="uf-amount-cell uf-totals-amount">
                            <span class="uf-currency">￥</span>
                            <span id="debit-total">0.00</span>
                        </td>
                        <td class="uf-amount-cell uf-totals-amount">
                            <span class="uf-currency">￥</span>
                            <span id="credit-total">0.00</span>
                        </td>
                        {% if mode != 'view' %}
                        <td class="uf-operations-cell">
                            <div class="uf-balance-indicator">
                                <span id="balance-status" class="uf-balance-text">平衡</span>
                            </div>
                        </td>
                        {% endif %}
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 签字区域 -->
        <div class="uf-signature-area">
            <div class="uf-signature-row">
                <div class="uf-signature-item">
                    <span class="uf-signature-label">制单:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">审核:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.reviewed_by %}{{ voucher.reviewed_by.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">记账:</span>
                    <span class="uf-signature-box">{% if voucher and voucher.posted_by %}{{ voucher.posted_by.username }}{% endif %}</span>
                </div>
                <div class="uf-signature-item">
                    <span class="uf-signature-label">出纳:</span>
                    <span class="uf-signature-box"></span>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="uf-status-bar">
            <div class="uf-status-left">
                <span class="uf-status-text">就绪</span>
                <span class="uf-status-separator">|</span>
                <span class="uf-status-text" id="current-time"></span>
            </div>
            <div class="uf-status-right">
                <span class="uf-status-text">用友财务软件</span>
            </div>
        </div>
    </div>
</div>

<!-- 用友风格科目选择窗口 -->
<div class="uf-modal" id="subjectModal" style="display: none;">
    <div class="uf-modal-backdrop" onclick="closeSubjectModal()"></div>
    <div class="uf-modal-dialog">
        <div class="uf-modal-content">
            <!-- 模态框标题栏 -->
            <div class="uf-modal-header">
                <div class="uf-modal-title">
                    <i class="uf-icon">📁</i>
                    <span>会计科目选择</span>
                </div>
                <button class="uf-modal-close" onclick="closeSubjectModal()">
                    <i class="uf-icon">✕</i>
                </button>
            </div>

            <!-- 工具栏 -->
            <div class="uf-modal-toolbar">
                <div class="uf-toolbar-group">
                    <button class="uf-btn uf-btn-primary" onclick="confirmSubjectSelection()">
                        <i class="uf-icon">✓</i> 确定
                    </button>
                    <button class="uf-btn" onclick="closeSubjectModal()">
                        <i class="uf-icon">✕</i> 取消
                    </button>
                </div>
                <div class="uf-toolbar-separator"></div>
                <div class="uf-search-group">
                    <input type="text" class="uf-form-control uf-search-input" id="subject-search"
                           placeholder="输入科目编码或名称搜索...">
                    <button class="uf-btn" onclick="searchSubjects()">
                        <i class="uf-icon">🔍</i> 搜索
                    </button>
                </div>
            </div>

            <!-- 科目列表 -->
            <div class="uf-modal-body">
                <div class="uf-subject-list-container">
                    <table class="uf-table uf-subject-table" id="subjectTable">
                        <thead>
                            <tr>
                                <th class="uf-col-code">科目编码</th>
                                <th class="uf-col-name">科目名称</th>
                                <th class="uf-col-type">科目类型</th>
                                <th class="uf-col-level">级次</th>
                                <th class="uf-col-direction">余额方向</th>
                            </tr>
                        </thead>
                        <tbody id="subjectTableBody">
                            <!-- 科目数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 状态栏 -->
            <div class="uf-modal-footer">
                <div class="uf-status-info">
                    <span id="subjectCount">共 0 个科目</span>
                    <span class="uf-status-separator">|</span>
                    <span id="selectedSubjectInfo">未选择科目</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}

<!-- 引入财务凭证相关JavaScript文件 -->
<script src="{{ url_for('static', filename='financial/js/pinyin-helper.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/amount-formatter.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/subject-selector.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/table-operations.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/ux-enhancements.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/voucher-editor.js') }}?v=1.0.0" nonce="{{ csp_nonce }}"></script>

<script nonce="{{ csp_nonce }}">
// 设置全局变量供外部JS文件使用
window.voucherMode = '{{ mode|default("create") }}';
window.voucherId = {% if voucher %}{{ voucher.id }}{% else %}null{% endif %};

// 兼容性变量（保留原有变量名）
let currentRow = 0;
let currentCol = 0;
let selectedSubjectCell = null;
let selectedSubject = null;

// 页面初始化 - 调用外部JS文件中的主要逻辑
$(document).ready(function() {
    console.log('🚀 用友风格凭证编辑器初始化...');

    // 确保日期字段有默认值
    const voucherDateField = $('#voucher-date');
    if (voucherDateField.length && !voucherDateField.val()) {
        const today = new Date().toISOString().split('T')[0];
        voucherDateField.val(today);
        console.log('设置默认日期:', today);
    }

    // 更新时间显示
    updateTime();
    setInterval(updateTime, 1000);

    // 等待外部JS文件加载完成后调用主要初始化函数
    setTimeout(function() {
        if (typeof initVoucherEditor === 'function') {
            console.log('📋 调用主要初始化函数...');
            initVoucherEditor();
        } else {
            console.error('❌ initVoucherEditor函数未找到，请检查voucher-editor.js是否正确加载');
        }
    }, 100);

    console.log('✅ 用友风格凭证编辑器初始化完成');
});

// 时间更新函数（保留在内联脚本中）
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    $('#current-time').text(timeString);
}

// 兼容性函数（保留部分函数名以兼容HTML中的onclick事件）
// 主要逻辑已移至外部JS文件，这里只保留必要的兼容性函数

// 工具栏按钮兼容性函数
function navigateToFirst() { console.log('导航到首张凭证'); }
function navigateToLast() { console.log('导航到末张凭证'); }
function navigateToVoucher() { console.log('跳转到指定凭证'); }
function copyVoucher() { console.log('复制凭证'); }
function reverseVoucher() { console.log('冲销凭证'); }
function insertRowAbove(button) { console.log('插入行'); }
function printPreview() { printVoucher(); }
function reviewVoucher() { console.log('审核凭证'); }
function unReviewVoucher() { console.log('取消审核'); }

// 科目选择模态框函数（调用外部JS文件中的实际实现）
function openSubjectModal(element) {
    if (typeof openUFSubjectModal === 'function') {
        openUFSubjectModal(element);
    } else {
        // 备用实现
        selectedSubjectCell = $(element).closest('.uf-subject-selector');
        $('#subjectModal').show();
        $('#subject-search').focus();
    }
}

function closeSubjectModal() {
    if (typeof closeUFSubjectModal === 'function') {
        closeUFSubjectModal();
    } else {
        // 备用实现
        $('#subjectModal').hide();
        selectedSubject = null;
        selectedSubjectCell = null;
    }
}

// 兼容性函数（调用外部JS文件中的实际实现）
function addRow() {
    if (typeof addUFRow === 'function') {
        addUFRow();
    } else {
        console.warn('addUFRow函数未找到');
    }
}

function deleteRow(button) {
    if (typeof deleteUFRow === 'function') {
        deleteUFRow(button);
    } else {
        console.warn('deleteUFRow函数未找到');
    }
}

function updateTotals() {
    if (typeof updateUFTotals === 'function') {
        updateUFTotals();
    } else {
        console.warn('updateUFTotals函数未找到');
    }
}

function checkBalance() {
    if (typeof checkUFBalance === 'function') {
        checkUFBalance();
    } else {
        console.warn('checkUFBalance函数未找到');
    }
}

function saveVoucher() {
    if (typeof saveUFVoucher === 'function') {
        saveUFVoucher();
    } else {
        console.warn('saveUFVoucher函数未找到');
    }
}

function printVoucher() {
    if (typeof printUFVoucher === 'function') {
        printUFVoucher();
    } else {
        console.warn('printUFVoucher函数未找到');
    }
}

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
// 这些函数被HTML中的onclick事件调用，需要保留

function insertRow() { console.log('插入行'); }
function copyRow() { console.log('复制行'); }

// 删除行函数（保留以兼容HTML中的onclick事件）
function deleteUFRow(button) {
    if (typeof deleteUFRow === 'function' && window.deleteUFRow !== deleteUFRow) {
        // 调用外部JS文件中的实现
        return window.deleteUFRow(button);
    }
    console.log('删除行');
}

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function updateRowNumbers() { console.log('更新行号'); }
function updateUFLineNumbers() { console.log('更新UF行号'); }

// 中文大写金额转换（保留在内联脚本中，因为是特定的业务逻辑）
function convertAmountToChinese(amount) {
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
    const decimals = ['角', '分'];

    if (amount === 0) return '零元整';

    let result = '';
    const amountStr = amount.toFixed(2);
    const [integerPart, decimalPart] = amountStr.split('.');

    // 处理整数部分
    for (let i = 0; i < integerPart.length; i++) {
        const digit = parseInt(integerPart[i]);
        const unitIndex = integerPart.length - i - 1;

        if (digit !== 0) {
            result += digits[digit] + units[unitIndex];
        } else if (result && !result.endsWith('零')) {
            result += '零';
        }
    }

    result += '元';

    // 处理小数部分
    if (decimalPart && decimalPart !== '00') {
        const jiao = parseInt(decimalPart[0]);
        const fen = parseInt(decimalPart[1]);

        if (jiao > 0) {
            result += digits[jiao] + '角';
        }
        if (fen > 0) {
            result += digits[fen] + '分';
        }
    } else {
        result += '整';
    }

    return result;
}

// 保留必要的兼容性变量
let subjectTree = [];
let filteredSubjects = [];

// 科目选择模态框函数（保留以兼容HTML中的onclick事件）
function buildUFSubjectTable() { console.log('构建科目表格'); }
function buildSubjectTree() { console.log('构建科目树'); }
function renderSubjectTree() { console.log('渲染科目树'); }
function toggleTreeNode(code) { console.log('切换树节点:', code); }
function selectTreeSubject(code) { console.log('选择树科目:', code); }
function confirmSubjectSelection() { console.log('确认科目选择'); }
function searchSubjects() { console.log('搜索科目'); }
function searchUFSubjects() { console.log('搜索UF科目'); }
function expandAllSubjects() { console.log('展开所有科目'); }
function collapseAllSubjects() { console.log('折叠所有科目'); }

// 保留必要的工具函数（用于科目级次计算等）
function getSubjectLevel(code) {
    if (code.length <= 4) return 1;
    if (code.length <= 6) return 2;
    if (code.length <= 8) return 3;
    return 4;
}

function getBalanceDirection(subjectType) {
    const directions = {
        '资产': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方',
        '费用': '借方',
        '成本': '借方'
    };
    return directions[subjectType] || '-';
}

// 保留必要的工具函数
function getParentSubjectCode(code) {
    if (code.length <= 4) return null;
    if (code.length <= 6) return code.substring(0, 4);
    if (code.length <= 8) return code.substring(0, 6);
    return code.substring(0, 8);
}

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function renderTreeNode() { console.log('渲染树节点'); }
function findNodeByCode() { console.log('查找节点'); }
function updateSubjectDetails() { console.log('更新科目详情'); }
function updateSelectedSubjectInfo() { console.log('更新选中科目信息'); }
function renderSearchResults() { console.log('渲染搜索结果'); }
function updateSubjectStatusBar() { console.log('更新状态栏'); }
function closeSubjectSelector() { console.log('关闭科目选择器'); }
function minimizeSubjectSelector() { console.log('最小化科目选择器'); }
function expandCollapseAll() { console.log('展开/折叠所有'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
// 这些函数被HTML中的onclick事件调用，需要保留函数名

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function collectVoucherData() { console.log('收集凭证数据'); }
function validateVoucherData() { console.log('验证凭证数据'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
// 这些函数被HTML中的onclick事件调用，需要保留函数名

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
// 这些函数被HTML中的onclick事件调用，需要保留函数名

function exportVoucher() {
    if (voucherId) {
        window.location.href = '{{ url_for("financial.voucher_text_view", id=999999) }}'.replace('999999', voucherId);
    }
}

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function insertUFRow() { console.log('插入行'); }
function insertRowAbove() { console.log('在上方插入行'); }

// 重复的updateTime函数已删除

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function showSubjectDropdown() { console.log('显示科目下拉框'); }
function buildSubjectOptions() { console.log('构建科目选项'); }
function handleSubjectKeydown() { console.log('处理科目键盘事件'); }
function navigateOptions() { console.log('导航选项'); }
function selectSubject() { console.log('选择科目'); }
function getSubjectPinyin() { console.log('获取科目拼音'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function filterSubjects() { console.log('筛选科目'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
// 这些函数被HTML中的onclick事件调用，需要保留函数名

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function prepareUFAmountEdit() { console.log('准备金额编辑'); }
function formatUFAmountInput() { console.log('格式化金额输入'); }
function finalizeUFAmountFormat() { console.log('完成金额格式化'); }
function formatUFAmountDisplay() { console.log('格式化金额显示'); }
function parseUFAmount() { console.log('解析金额'); }
function updateAmountInputState() { console.log('更新金额状态'); }
function validateUFAmount() { console.log('验证金额'); }
function getFormattedAmountText() { console.log('获取格式化金额文本'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function showUFMessage() { console.log('显示UF消息'); }
function hasUnsavedChanges() { return false; }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function initUFKeyboardShortcuts() { console.log('初始化快捷键'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function showUFHelp() { console.log('显示帮助'); }
function autoResizeTextarea() { console.log('自动调整文本区'); }
function initSummaryAutoResize() { console.log('初始化摘要自动调整'); }

// 保留必要的兼容性函数（主要逻辑已移至外部JS文件）
function optimizeTableLayout() { console.log('优化表格布局'); }

// 重复的初始化代码已删除

</script>
{% endblock %}
