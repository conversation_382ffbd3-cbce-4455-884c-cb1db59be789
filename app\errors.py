"""
错误处理模块
处理各种HTTP错误和应用程序异常
"""

from flask import render_template, request, jsonify, current_app
import logging

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        """处理400错误 - 错误请求"""
        # 检查是否是SSL/TLS握手错误
        if request.environ.get('REQUEST_METHOD') == 'GET' and not request.path:
            current_app.logger.warning(f"SSL/TLS握手错误来自IP: {request.environ.get('REMOTE_ADDR', 'unknown')}")
            return '', 400
        
        if request.is_json:
            return jsonify({
                'success': False,
                'message': '请求格式错误',
                'error_code': 400
            }), 400
        
        return render_template('errors/400.html'), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """处理401错误 - 未授权"""
        if request.is_json:
            return jsonify({
                'success': False,
                'message': '请先登录',
                'error_code': 401,
                'redirect': '/login'
            }), 401
        
        return render_template('errors/401.html'), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        """处理403错误 - 禁止访问"""
        if request.is_json:
            return jsonify({
                'success': False,
                'message': '没有权限访问此资源',
                'error_code': 403
            }), 403
        
        return render_template('errors/403.html'), 403
    
    @app.errorhandler(404)
    def not_found(error):
        """处理404错误 - 页面未找到"""
        if request.is_json:
            return jsonify({
                'success': False,
                'message': '请求的资源不存在',
                'error_code': 404
            }), 404
        
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """处理500错误 - 服务器内部错误"""
        current_app.logger.error(f"服务器内部错误: {str(error)}")

        if request.is_json:
            return jsonify({
                'success': False,
                'message': '服务器内部错误，请稍后重试',
                'error_code': 500
            }), 500

        return render_template('errors/500.html'), 500

    @app.errorhandler(505)
    def http_version_not_supported(error):
        """处理505错误 - HTTP版本不支持"""
        client_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        current_app.logger.warning(f"HTTP版本不支持错误来自IP: {client_ip}")

        # 记录可能的HTTP/2.0请求
        if 'HTTP/2.0' in str(error) or request.environ.get('SERVER_PROTOCOL') == 'HTTP/2.0':
            log_security_event('HTTP/2.0请求', f'客户端尝试使用HTTP/2.0协议', client_ip)

        return jsonify({
            'success': False,
            'message': 'HTTP版本不支持，请使用HTTP/1.1',
            'error_code': 505
        }), 505

    @app.errorhandler(429)
    def too_many_requests(error):
        """处理429错误 - 请求过多"""
        client_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        current_app.logger.warning(f"请求频率限制触发，IP: {client_ip}")

        if request.is_json:
            return jsonify({
                'success': False,
                'message': '请求过于频繁，请稍后重试',
                'error_code': 429,
                'retry_after': 60
            }), 429

        return render_template('errors/429.html'), 429
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        """处理未捕获的异常"""
        current_app.logger.error(f"未捕获的异常: {str(error)}", exc_info=True)
        
        # 如果是开发环境，显示详细错误信息
        if current_app.debug:
            raise error
        
        if request.is_json:
            return jsonify({
                'success': False,
                'message': '系统发生错误，请联系管理员',
                'error_code': 500
            }), 500
        
        return render_template('errors/500.html'), 500

def log_security_event(event_type, details, ip_address=None):
    """记录安全事件"""
    if not ip_address:
        ip_address = request.environ.get('REMOTE_ADDR', 'unknown')
    
    current_app.logger.warning(f"安全事件 [{event_type}] 来自IP {ip_address}: {details}")
