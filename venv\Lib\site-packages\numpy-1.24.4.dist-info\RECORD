../../Scripts/f2py.exe,sha256=qVlm0VIxgzMPANyrOwzYmiXu_mT9JTwuwdUXSVwvbsI,108392
numpy-1.24.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-1.24.4.dist-info/LICENSE.txt,sha256=vg6QnWgZBt4Cd0OxlFGpvpXHYFvF6fh_Apgl-n7-T80,48809
numpy-1.24.4.dist-info/LICENSES_bundled.txt,sha256=UWGZ0f1YKTYB3hrVHM26mHuBuGTJwRn50NMTMyInvPY,656
numpy-1.24.4.dist-info/METADATA,sha256=kBZ3ucbglz7ZHs5aefrTxC2v2ITh1ymc9cOSp-fGI5g,5556
numpy-1.24.4.dist-info/RECORD,,
numpy-1.24.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-1.24.4.dist-info/WHEEL,sha256=P24hRDQapckAvHvgBBzlH5uw6rQ_oZQtKbG6drGrqOM,100
numpy-1.24.4.dist-info/entry_points.txt,sha256=9Y2FuDxJ4f1QPr79jh1PHP5xheTjHqUjYMwiFml8ZLQ,144
numpy-1.24.4.dist-info/top_level.txt,sha256=4J9lbBMLnAiyxatxh8iRKV5Entd_6-oqbO7pzJjMsPw,6
numpy/.libs/libopenblas64__v0.3.21-gcc_10_3_0.dll,sha256=FMVDxBjnGdjRk_-JDBr-rP7fV0lYO80HmBIYPn2QSqs,35875883
numpy/LICENSE.txt,sha256=vg6QnWgZBt4Cd0OxlFGpvpXHYFvF6fh_Apgl-n7-T80,48809
numpy/__config__.py,sha256=YzmWcMBEO6UpS4N50wsDKf7FnXEGZprMJMp-SNe_ruQ,5250
numpy/__init__.cython-30.pxd,sha256=MZ-QfBGc8pNUong-Glaav9B3kitexUsE-Y0LvWt6fjA,37268
numpy/__init__.pxd,sha256=_K5nNmULH7W9bSf9PN1n-wFVLOa4d5LX891UFiX3dh4,35601
numpy/__init__.py,sha256=lEvfVfkHZOLici6pSU4nTQFh2jooGeUf-AMgaORv-6k,16613
numpy/__init__.pyi,sha256=Uc2KUWNMrZjZgMo1wOO-zuae49y2EqJWjL7EyL1W0qg,157726
numpy/__pycache__/__config__.cpython-38.pyc,,
numpy/__pycache__/__init__.cpython-38.pyc,,
numpy/__pycache__/_distributor_init.cpython-38.pyc,,
numpy/__pycache__/_globals.cpython-38.pyc,,
numpy/__pycache__/_pytesttester.cpython-38.pyc,,
numpy/__pycache__/_version.cpython-38.pyc,,
numpy/__pycache__/conftest.cpython-38.pyc,,
numpy/__pycache__/ctypeslib.cpython-38.pyc,,
numpy/__pycache__/dual.cpython-38.pyc,,
numpy/__pycache__/matlib.cpython-38.pyc,,
numpy/__pycache__/setup.cpython-38.pyc,,
numpy/__pycache__/version.cpython-38.pyc,,
numpy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
numpy/_globals.py,sha256=6TRlrVbQKGY3L0vUy363uM5KWB3MIyQcmC_Gcwz7jmM,4010
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-38.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-38.pyc,,
numpy/_pyinstaller/__pycache__/pyinstaller-smoke.cpython-38.pyc,,
numpy/_pyinstaller/__pycache__/test_pyinstaller.cpython-38.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=H5-JbToSn8AvxYFdGvkB8nc4-6RpQLsftBkE-QKNcAc,1462
numpy/_pyinstaller/pyinstaller-smoke.py,sha256=xt3dl_DjxuzVTPrqmVmMOZm5-24wBG2TxldQl78Xt1g,1175
numpy/_pyinstaller/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=H5nsCpjRq6SIVoAMtWZKq6i3bVdLIs0QYwY7QJkEMXs,6874
numpy/_pytesttester.pyi,sha256=naZg3QsbkOp4kcjmCJzs3A5vmkLp1WynIgD5hUKHLZI,507
numpy/_typing/__init__.py,sha256=l0z4V04eQtOQgvjfSM8LcuUD572GthMkLmRDWbLvKoE,7333
numpy/_typing/__pycache__/__init__.cpython-38.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-38.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-38.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-38.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-38.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-38.pyc,,
numpy/_typing/__pycache__/_generic_alias.cpython-38.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-38.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-38.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-38.pyc,,
numpy/_typing/__pycache__/_shape.cpython-38.pyc,,
numpy/_typing/__pycache__/setup.cpython-38.pyc,,
numpy/_typing/_add_docstring.py,sha256=Fg_yvCZQLR2j-v8AXZ_qP80ad3RQalvVBhDQqaq8g2g,4077
numpy/_typing/_array_like.py,sha256=9hcHcaW7Yw1Nmk1FCWWg_4i2oR09HFJfr7RF_o9ZDP4,4556
numpy/_typing/_callable.pyi,sha256=cL3pdTVuhl7NYqBp90z5XohMauL2CyzE-eBv09A-pfs,11462
numpy/_typing/_char_codes.py,sha256=DRzmblPgrH3RPdOr0Hu3M3HA7Zpp_00ZEH_f7Bg5FN4,6027
numpy/_typing/_dtype_like.py,sha256=T7pcQhpm0bbiywmCJcRotUBWXBRJuwV3cgn-2OboW-w,5877
numpy/_typing/_extended_precision.py,sha256=mq4J2bIKeD8XGPTauPuAlwqevkywiZZBCCvsiRBvrdA,1154
numpy/_typing/_generic_alias.py,sha256=izAvdFNoenW_YEp2hEAXdUtZfMsA1lxBBhlEf2zd-aY,7724
numpy/_typing/_nbit.py,sha256=jo5eJF4zrw7QdDw1dIEKLIIPiLM-1tEn95YbDXrdSPs,361
numpy/_typing/_nested_sequence.py,sha256=4BAG96jPT5SwWnwgnDKB6fM_FCfZo7C1DWlS_Z4d6BU,2793
numpy/_typing/_scalars.py,sha256=V8LKjaaK6eSHVGRKCZIO1MvpGHNjYYMNeHSEfavLgQQ,987
numpy/_typing/_shape.py,sha256=ewHNno8A6Eg3Fl-9JA5jguDk5Q56mEqHjD-vr3VjINY,197
numpy/_typing/_ufunc.pyi,sha256=s1oqnjbiFVZQBXsF8t_ntsWsPp7ULJojQs1EN11OQS4,13083
numpy/_typing/setup.py,sha256=U68U8y7UUiNDVNYps2dY41DHWdyqeO1Uis0ByzlKPd4,347
numpy/_version.py,sha256=UZJKBgNii4Rh5St8ezpVGzinORrHLXIpojMMadAWK6w,519
numpy/array_api/__init__.py,sha256=oWkiG-iYjAnFw4TIYVd43uKy3FKD7p9pQkorUDZaEQE,10598
numpy/array_api/__pycache__/__init__.cpython-38.pyc,,
numpy/array_api/__pycache__/_array_object.cpython-38.pyc,,
numpy/array_api/__pycache__/_constants.cpython-38.pyc,,
numpy/array_api/__pycache__/_creation_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_data_type_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_dtypes.cpython-38.pyc,,
numpy/array_api/__pycache__/_elementwise_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_manipulation_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_searching_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_set_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_sorting_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_statistical_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/_typing.cpython-38.pyc,,
numpy/array_api/__pycache__/_utility_functions.cpython-38.pyc,,
numpy/array_api/__pycache__/linalg.cpython-38.pyc,,
numpy/array_api/__pycache__/setup.cpython-38.pyc,,
numpy/array_api/_array_object.py,sha256=6HdfOpqujDU_9SzZo2_7rTK5TQ39ysWwPB3f2WuV2uM,44344
numpy/array_api/_constants.py,sha256=l8tz1bWuEXiyzFAHCnx6_yPI4eXoZ4847Zn8yFIO_ow,72
numpy/array_api/_creation_functions.py,sha256=WqPmEGSOQXxoqug_ew0iO1SmEsNjJzmOn4xMRfYT1lA,10401
numpy/array_api/_data_type_functions.py,sha256=Eg3NbGuUnP65kIzPzHfjaC-odCoKXe-MP4UovxTGJ4U,4626
numpy/array_api/_dtypes.py,sha256=uKDX6j3LSsuH0-dHp_dP5IUxEGkUBdIy7qTX5DZGjQg,3850
numpy/array_api/_elementwise_functions.py,sha256=99m6md2SErPzugfltgotPphLEfB3KVgl1bwLUFiGpH4,25501
numpy/array_api/_manipulation_functions.py,sha256=dfiDNx62DFqi1MgOwQNBQr9_QBR9BXIIcVa2IX1y5cg,3106
numpy/array_api/_searching_functions.py,sha256=eL_gRYZ3Y6Z3OCu6qXl3y79dtnhCsGpeMZYwSVyI9wQ,1504
numpy/array_api/_set_functions.py,sha256=RlYmM18U-LClmWMBH_IRdpJE-ocffgwbiGzWxWDHzxU,3054
numpy/array_api/_sorting_functions.py,sha256=9lzRR1hVFsWe-LYIy7l85JX6HduoIGi7Q8RUZ2avd0s,1803
numpy/array_api/_statistical_functions.py,sha256=lHtSE3pvpuy2TfzYp7F-hnK6nGNJ5bU0_g1FgtMDqk4,3493
numpy/array_api/_typing.py,sha256=MN5OX-mCYT8bLegZFGXoCDRDfHRp5WSSOrS9kmShU9s,1450
numpy/array_api/_utility_functions.py,sha256=LjVxM__wjulID49MF5jeLkCDQuRB50KDUPdvHgAtnmg,861
numpy/array_api/linalg.py,sha256=HolcpcF8umMrT8IaOJ-jSHt2bJ5YWavIbQsigyC0FLQ,18306
numpy/array_api/setup.py,sha256=MrEzBh9m4-EV4iekcvUESVM3MW0bHJ5VvXaaTzMFZOU,353
numpy/array_api/tests/__init__.py,sha256=afUKDkt_1ajE1TzYGn4cTp-jMMBfx8riogjk3AePPf0,289
numpy/array_api/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_array_object.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_creation_functions.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_data_type_functions.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_elementwise_functions.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_set_functions.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_sorting_functions.cpython-38.pyc,,
numpy/array_api/tests/__pycache__/test_validation.cpython-38.pyc,,
numpy/array_api/tests/test_array_object.py,sha256=KnaBt03HwVCGGVFNAKGnESM5jva1cV7brZRF6RKEv2U,16146
numpy/array_api/tests/test_creation_functions.py,sha256=0AuzJkFyCLw102ilOCE9lt6q24ERhYVJqqdUKfIhjoQ,5165
numpy/array_api/tests/test_data_type_functions.py,sha256=suUSFbRFNG2oXmAmOadKWUExlQ_cfkZbo4JdYZCIOO0,441
numpy/array_api/tests/test_elementwise_functions.py,sha256=LfMHBMc-nudbY1r4v3a_aFCV6cX8jvDY3kzzyeju-u8,3730
numpy/array_api/tests/test_set_functions.py,sha256=EDUenQPYY47kxAthUP36Vbm0Bi3cgUt2v1c_OiK5Dfg,565
numpy/array_api/tests/test_sorting_functions.py,sha256=_5tZrT-vhYGHDFQCJEj3VWM1CQ0lwbZdW4FVvGz4R_A,625
numpy/array_api/tests/test_validation.py,sha256=UkU6SXeUCkgTuL0GK1NmsK_BVOt6SNeDzUlKbWdr63o,703
numpy/compat/__init__.py,sha256=kryOGy6TD3f9oEXy1sZZOxEMc50A7GtON1yf0nMPzr8,450
numpy/compat/__pycache__/__init__.cpython-38.pyc,,
numpy/compat/__pycache__/_inspect.cpython-38.pyc,,
numpy/compat/__pycache__/_pep440.cpython-38.pyc,,
numpy/compat/__pycache__/py3k.cpython-38.pyc,,
numpy/compat/__pycache__/setup.cpython-38.pyc,,
numpy/compat/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/compat/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
numpy/compat/py3k.py,sha256=LGUGlMxuDsrpxC3nLBgUEdr5ep-QP3udbDti_5q0wro,4077
numpy/compat/setup.py,sha256=PmRas58NGR72H-7OsQj6kElSUeQHjN75qVh5jlQIJmc,345
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/compat/tests/__pycache__/test_compat.cpython-38.pyc,,
numpy/compat/tests/test_compat.py,sha256=HJLePpHpc45UqzuUpvKx3R1r7i1EwUYmCQxFpIb7nD0,990
numpy/conftest.py,sha256=Lb8cMn-OMyODrYMg_jU2zMBIy2QLQOaugZNEuM6QhKo,4664
numpy/core/__init__.py,sha256=4kR753sWrwOu3Hfw_fhBSsZVpN2YKo8Do08jhu6Zw1k,5928
numpy/core/__init__.pyi,sha256=nBhfHv0Vy8Cc37vQn3joQShGEvBy4fyo5aBahLSj5Xo,128
numpy/core/__pycache__/__init__.cpython-38.pyc,,
numpy/core/__pycache__/_add_newdocs.cpython-38.pyc,,
numpy/core/__pycache__/_add_newdocs_scalars.cpython-38.pyc,,
numpy/core/__pycache__/_asarray.cpython-38.pyc,,
numpy/core/__pycache__/_dtype.cpython-38.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-38.pyc,,
numpy/core/__pycache__/_exceptions.cpython-38.pyc,,
numpy/core/__pycache__/_internal.cpython-38.pyc,,
numpy/core/__pycache__/_machar.cpython-38.pyc,,
numpy/core/__pycache__/_methods.cpython-38.pyc,,
numpy/core/__pycache__/_string_helpers.cpython-38.pyc,,
numpy/core/__pycache__/_type_aliases.cpython-38.pyc,,
numpy/core/__pycache__/_ufunc_config.cpython-38.pyc,,
numpy/core/__pycache__/arrayprint.cpython-38.pyc,,
numpy/core/__pycache__/cversions.cpython-38.pyc,,
numpy/core/__pycache__/defchararray.cpython-38.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-38.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-38.pyc,,
numpy/core/__pycache__/function_base.cpython-38.pyc,,
numpy/core/__pycache__/generate_numpy_api.cpython-38.pyc,,
numpy/core/__pycache__/getlimits.cpython-38.pyc,,
numpy/core/__pycache__/memmap.cpython-38.pyc,,
numpy/core/__pycache__/multiarray.cpython-38.pyc,,
numpy/core/__pycache__/numeric.cpython-38.pyc,,
numpy/core/__pycache__/numerictypes.cpython-38.pyc,,
numpy/core/__pycache__/overrides.cpython-38.pyc,,
numpy/core/__pycache__/records.cpython-38.pyc,,
numpy/core/__pycache__/setup.cpython-38.pyc,,
numpy/core/__pycache__/setup_common.cpython-38.pyc,,
numpy/core/__pycache__/shape_base.cpython-38.pyc,,
numpy/core/__pycache__/umath.cpython-38.pyc,,
numpy/core/__pycache__/umath_tests.cpython-38.pyc,,
numpy/core/_add_newdocs.py,sha256=0k_iLv-GE1pD4eGoREe7DC2vnlACK5CFDWjO58BCg9Y,216145
numpy/core/_add_newdocs_scalars.py,sha256=NEzuQKYPDnOdNUuCH476ZIwCxrSwVC3-ojMrCqfV8yE,12449
numpy/core/_asarray.py,sha256=XIwjFUtP3qVHsu5x-dopCqADScAjwUe8oBbSE6bUy3k,4163
numpy/core/_asarray.pyi,sha256=x8vOeIXbYX7nrKslV8YvljEvnhFHYLCyLnQwNYbKQEU,1093
numpy/core/_dtype.py,sha256=84AysTTmVd7XOlM-miGaTltiG0CxWh-zgm0QBIit02U,10860
numpy/core/_dtype_ctypes.py,sha256=O8tYBqU1QzCG1CXviBe6jrgHYnyIPqpci9GEy9lXO08,3790
numpy/core/_exceptions.py,sha256=PoPre-AeWRwTh7i6DMYFsJlFwcnIZDMhu6rmtSjirz0,8895
numpy/core/_internal.py,sha256=Eilpd26U8MxYBL_mOdiaoGon7iw7qZIXAdh2XygCm5k,29127
numpy/core/_internal.pyi,sha256=HBjDwzplwD0lAEzYt4Xl4y2O_RjBLP1jcuT9XudVPLU,1062
numpy/core/_machar.py,sha256=CIPTcIuXRjAIjonKNk7b67DEu15TE0b1m_cccyQc2gU,11975
numpy/core/_methods.py,sha256=Z6HofPsdYFfusnNLRK17MuGLUseSvg9Mi20gpSR0rZI,11365
numpy/core/_multiarray_tests.cp38-win_amd64.pyd,sha256=vh3bROnwXMCu3RyqQIzk3Cjqb2_lB8yPNd8Aq6TVfMU,66560
numpy/core/_multiarray_umath.cp38-win_amd64.pyd,sha256=se_feSo9LMMOfcw73ZEcvP1S1HAxx8bejazlITEck_4,2776064
numpy/core/_operand_flag_tests.cp38-win_amd64.pyd,sha256=xsKU211YHqgyAx-qTVwif_dOsZ6pJKaxKHYgC2VEIZo,12288
numpy/core/_rational_tests.cp38-win_amd64.pyd,sha256=qf5zn75Xow9b6qyw6GY5ZRBG7T6fIblkOKBTURQoxmQ,40960
numpy/core/_simd.cp38-win_amd64.pyd,sha256=jP1jzoVkPC-DkxgYVxjniMLDWZAWzMLMPvJgvqg5dR0,1330176
numpy/core/_string_helpers.py,sha256=xFVFp6go9I8O7PKRR2zwkOk2VJxlnXTFSYt4s7MwXGM,2955
numpy/core/_struct_ufunc_tests.cp38-win_amd64.pyd,sha256=5zz2-iRcCm_hnzb6P-tINQm6BYI-WH1QNqWdVR_YmD4,14336
numpy/core/_type_aliases.py,sha256=8-qoE3t_63JD5GzGTCCGGBnIGYi7n_7Pc6niKnEedeo,7779
numpy/core/_type_aliases.pyi,sha256=uccb0D594Wq_yu0ez4ARoW5XLj6R1vy-E-vWJXywnhk,387
numpy/core/_ufunc_config.py,sha256=zzouJpB7LMtSGmxpPoisgWIlAchTvs1ypkIJOrhD4dg,14412
numpy/core/_ufunc_config.pyi,sha256=KgouzlKtzHKm3UquSj9kbLI9WAQqpL3bFMmAW6-V4yw,1103
numpy/core/_umath_tests.cp38-win_amd64.pyd,sha256=sYtM5nXH5iZ8lhPFl93UufLzQ4V_8eknMPuVeHDhmmE,30208
numpy/core/arrayprint.py,sha256=VuRfuoAeAW_MWVKuXGBKwJiLP2naSfVIcCvREBhciYw,64418
numpy/core/arrayprint.pyi,sha256=8fKy1vuqDh5XbDUg9824pp1QKKf2fIIedlzwtU2DCko,4570
numpy/core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/core/defchararray.py,sha256=lJPpYmB1yjJnjlV8st_gWqSLx_ulg5bioFG2GlM_dG0,75925
numpy/core/defchararray.pyi,sha256=e9Xi4m3OgIml9TftRkpSM6uctdLaNdEbqnZtkF8kSJ0,9637
numpy/core/einsumfunc.py,sha256=24VA1C9Urm3HOCDEJgYgNn3miEmd3F_1Gb99zXDJykA,53312
numpy/core/einsumfunc.pyi,sha256=-AuAhafq6bHIQNddoMmTFfpVdvoJgY3G0-0U8oxYhpg,3922
numpy/core/fromnumeric.py,sha256=4hmtWbzJNvEp6-bYIGZXGyYemeCwwh3LaQnHXgMoQBQ,129034
numpy/core/fromnumeric.pyi,sha256=ThsXpqMeh8DWUGZQi5TiDBZXUxhXp9auC3Vb_tLVTO0,24521
numpy/core/function_base.py,sha256=TynEwPfce2auYDL3K5ZauZR68PqAiKCiZA0r7X5mzw4,19820
numpy/core/function_base.pyi,sha256=iqCNWDs25nUsCXqt2NBwxNqewlEJYfFiIraqBJXv1h4,4912
numpy/core/generate_numpy_api.py,sha256=lu_QzObO-9cO2igNxp0vgnYyTfJSIKeX-bo2ZwQ8H0c,7649
numpy/core/getlimits.py,sha256=8QLShfnF3qWpZuBmL-ttYttEtBLzOJud7zPLnSjDlRY,25607
numpy/core/getlimits.pyi,sha256=rRrU4RZYBrsczGZq6_VRCBNwUKyrqPAHARXXLQuw950,88
numpy/core/include/numpy/.doxyfile,sha256=kISUwZgVeWl649njq6TmhQiyqzPciaZ_QS9e1Q62pOE,60
numpy/core/include/numpy/__multiarray_api.h,sha256=AQ8wKEWPOD_H7PXWzFkeQTUlBSHbcEMdQVtdPfwBI4g,64273
numpy/core/include/numpy/__ufunc_api.h,sha256=H7-TsFbz-CmyBFceTYhNcOm-GltjuAVLebBjctaRtA4,12925
numpy/core/include/numpy/_neighborhood_iterator_imp.h,sha256=sItfdxPWMM6KEBwCpNLC3VoSQVb4usMUC40zOK4zE_s,1967
numpy/core/include/numpy/_numpyconfig.h,sha256=hcnzpXmNhBtYvFVrCpMxt0nDZjoM2Ff4kmJ7nDFdT40,851
numpy/core/include/numpy/arrayobject.h,sha256=f1YdhtzB7wAHDQwmClaFwOl3U-cxkm2UJqzpfQNyhOs,294
numpy/core/include/numpy/arrayscalars.h,sha256=ONfMOpGH6KLorCBcc8265NkFse4x1sZ5ZlnMnC4kM60,4000
numpy/core/include/numpy/experimental_dtype_api.h,sha256=g_lkqOVw5wfu1I5gbhbX1L0dERzoRaJQ1Zygx1sQSYE,20445
numpy/core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/core/include/numpy/libdivide/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/core/include/numpy/libdivide/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/core/include/numpy/multiarray_api.txt,sha256=T4oXbfALaUN0_LJ5PopncOF1j6EK8FGJ3zH9O02tRQg,59879
numpy/core/include/numpy/ndarrayobject.h,sha256=Ar9_P9KCJBRECa8ZvJhlODs4Z_S_vI9HpQKjVbauzUs,10442
numpy/core/include/numpy/ndarraytypes.h,sha256=jUwbK_yyblIP_SR5XyuKwU10rvXcatIGIc6kUbzjafA,70644
numpy/core/include/numpy/noprefix.h,sha256=QYUdrHLYEaooNNXnazQYo5WpqPxHedEVXlOAGP7oECo,7041
numpy/core/include/numpy/npy_1_7_deprecated_api.h,sha256=dX_2cI125SpW88dJEtryt-wO4pHxzKR4CcODfjFwtms,4451
numpy/core/include/numpy/npy_3kcompat.h,sha256=GvHO40vGFTrpAsjyoV1oqDj_ReGSum7jnDTAk6D3Zfg,16587
numpy/core/include/numpy/npy_common.h,sha256=t49cyx1laCI6wz_T8FJKf8DCad5RuQW53xPHXAqp18U,40137
numpy/core/include/numpy/npy_cpu.h,sha256=tlYFBhOC6yswF9fZTxWlH2U8nBoOchnygVU5wiTR-5c,4732
numpy/core/include/numpy/npy_endian.h,sha256=G3x4fuvRgY6_Y0AWiJaQ5ZbtmMRRt_QUnYCwkqrHhPE,2863
numpy/core/include/numpy/npy_interrupt.h,sha256=9KhrOhicl4TW3pP6wugWNz5pDDvp9SroisKE04Bbaew,2004
numpy/core/include/numpy/npy_math.h,sha256=nMSrgrw-Fd-NruWSV2g47s5qcbeZhoFwzlNitJJRWHQ,20464
numpy/core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/core/include/numpy/npy_os.h,sha256=BB4JoiyTtlBFFHjn7aLe2t47Qq5R7cY-efyaQk_py14,1156
numpy/core/include/numpy/numpyconfig.h,sha256=FwNVOCnrEq0CXfZn49Zi9hcMs5upo3An7d4_hSoucf0,3027
numpy/core/include/numpy/old_defines.h,sha256=jGkDx_FahMvHMTuoWFvx5g5bCV8btUj9pgYNoo_PtwA,6592
numpy/core/include/numpy/oldnumeric.h,sha256=2d5tfBGHavnHpQJUvX90ZJFkgsEF8TlpqXhHNUuE_54,931
numpy/core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/core/include/numpy/random/distributions.h,sha256=mzuU4dT5sv78rg5IHy6Ip1rHGfrbstMtk_EIja5_Jds,10074
numpy/core/include/numpy/ufunc_api.txt,sha256=Qnbm8bUW9JXlOmCaX-cGztnUK-QKj0ecPPeNm6ZEgv0,7533
numpy/core/include/numpy/ufuncobject.h,sha256=TW8ccfYFcCMWZDHGyLkva_wlk9HZ2ewdphgHmDRAeKo,12252
numpy/core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/core/lib/npy-pkg-config/mlib.ini,sha256=mQBSOI6opCVmMZK4vwIhLe5J7YevO3PbaHsI0MlJGHs,151
numpy/core/lib/npy-pkg-config/npymath.ini,sha256=5dwvhvbX3a_9toniEDvGPDGChbXIfFiLa36H4YOR-vw,380
numpy/core/lib/npymath.lib,sha256=DNOrnhWOwNp3kXF_vHkNOklmfBbOVM4uAcXtCcXPZik,142836
numpy/core/memmap.py,sha256=_XXONlK35hVQqljype2sNt43fNAhBtBlkJwnedqTrnw,12025
numpy/core/memmap.pyi,sha256=pwOLGTEk20Z2Tao7hqxLDqfCHrO10otgozEsUO-bPeo,58
numpy/core/multiarray.py,sha256=sD0w9fX_cKiH3LLd2B8jgA07SG4LcOWP3rQVVgPxjCk,57701
numpy/core/multiarray.pyi,sha256=M0IU-OjdBudnmHqIN_tdyz1kqNId-aOsCXVtMIq0Eq0,25782
numpy/core/numeric.py,sha256=uFubQnjbQfoQ9rVT6wPu_StJTg5ZG7fQkT9t6KILsyo,80263
numpy/core/numeric.pyi,sha256=XZrxwES5VtGb_xIBGga6XRooFyxzUH2yfAH8zRsgO9g,14887
numpy/core/numerictypes.py,sha256=LhX6YK-TjzFGEfGoEo780SKd7FSEWFEjU4_mgLUooXM,17909
numpy/core/numerictypes.pyi,sha256=MzJCuO3afaM89bG3FbLHSfFbZXkkozfl6s_m08twJWQ,3549
numpy/core/overrides.py,sha256=uP9pDMshtpdqw7CRfr4uiV8Thc8tLRWZuCtu-iMGW6w,8573
numpy/core/records.py,sha256=qCYrXWgdiyAwbiaos3mgUiLZBQyvzlu3RAlypLkpDa0,38644
numpy/core/records.pyi,sha256=6qSYt2FKZytTswMFqcTrrqVi4HEbLXh0_gB2Graq78o,5926
numpy/core/setup.py,sha256=IlbKDKj7xemjIxvjQ0145xGTUHD_Eoos6igIDjG-GJs,53554
numpy/core/setup_common.py,sha256=TDbsuliK_MAPm4zBhd9xXjuWGJQo2XYdQYjObJPHWIs,19995
numpy/core/shape_base.py,sha256=HWXQHrTG9Ywied0YmsY4zMH9UibX0MokdyQoB234Rzk,31425
numpy/core/shape_base.pyi,sha256=mR9_E24Qll2NT_80QX2TiRkSozB_y6ZyvcNwSyi5-QQ,2897
numpy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/core/tests/__pycache__/_locales.cpython-38.pyc,,
numpy/core/tests/__pycache__/test__exceptions.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_abc.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_api.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_argparse.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_array_coercion.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_array_interface.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_arraymethod.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_arrayprint.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_casting_unittests.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_conversion_utils.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_cpu_dispatcher.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_cpu_features.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_custom_dtypes.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_cython.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_datetime.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_defchararray.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_deprecations.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_dlpack.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_dtype.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_einsum.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_errstate.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_extint128.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_function_base.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_getlimits.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_half.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_hashtable.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_indexerrors.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_indexing.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_item_selection.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_limited_api.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_longdouble.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_machar.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_mem_overlap.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_mem_policy.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_memmap.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_multiarray.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_nditer.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_nep50_promotions.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_numeric.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_numerictypes.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_overrides.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_print.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_protocols.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_records.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalar_ctors.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalar_methods.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalarbuffer.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalarinherit.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalarmath.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_scalarprint.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_shape_base.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_simd.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_simd_module.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_strings.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_ufunc.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_umath.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_umath_accuracy.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_umath_complex.cpython-38.pyc,,
numpy/core/tests/__pycache__/test_unicode.cpython-38.pyc,,
numpy/core/tests/_locales.py,sha256=esGp_wCqPpxFxy3eUF-r_Wk-yjFjrQEwkgSzolRzUr0,2280
numpy/core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/core/tests/data/generate_umath_validation_data.cpp,sha256=72bn3HBT3sS7uIIWKKSgW1VtNZpRlSZM-OsaKbh0cQA,6010
numpy/core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/core/tests/data/umath-validation-set-arccos.csv,sha256=8mDga_qwTZoPMm1UHPAqjLfBKTHTW5PT5sTeSQhg8pI,62794
numpy/core/tests/data/umath-validation-set-arccosh.csv,sha256=RN30zA_HBqlPb4UwCfk3gQMYNSopV765CQWnhG2Lx0g,62794
numpy/core/tests/data/umath-validation-set-arcsin.csv,sha256=TMvZI0veNaOHupfGPvS_pTRfX0yH33SoaQWT6Q9Epsc,62768
numpy/core/tests/data/umath-validation-set-arcsinh.csv,sha256=GFRD_4CZTEH47C71CWC6pVSWkJFMgxdii3rJXV3RAkw,61718
numpy/core/tests/data/umath-validation-set-arctan.csv,sha256=EFyJjE5dr5VBPLKlFf_7ZVI_s8Wx7FswdHEzs1mpYr8,61734
numpy/core/tests/data/umath-validation-set-arctanh.csv,sha256=0_cOGarj-biMitr6L1ZsBafWfDpecSOf-pk96wVOpIA,62768
numpy/core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/core/tests/data/umath-validation-set-cos.csv,sha256=qeR-Dg9vJB2Xz63q2b50tWUO1i0TWHMgYC75XGiy-AY,60497
numpy/core/tests/data/umath-validation-set-cosh.csv,sha256=FoMRNGCkmjaAspkoZ6EOTRkcUCUxnWdcq2NHfMyaPXg,62298
numpy/core/tests/data/umath-validation-set-exp.csv,sha256=q7AFuKS3D9HRm01wby5b0aZhbBF-eFmniO-NIyuHEpo,17903
numpy/core/tests/data/umath-validation-set-exp2.csv,sha256=ruPfs9R5l8NU43eP3HSiJYMzJMQkD0W5XwpkFRcVZNI,60053
numpy/core/tests/data/umath-validation-set-expm1.csv,sha256=onF_9BcC7wV_dkSRgazWVe9WaEOijyoBGUYSmaEc7OM,61728
numpy/core/tests/data/umath-validation-set-log.csv,sha256=vp1nbu--u7rV8dg9bDLUteLOfZBe5s4Uwyhll15g4AY,11963
numpy/core/tests/data/umath-validation-set-log10.csv,sha256=Gy6aRCYcWMBxTLIOLY9_zWymevNOGlc8cy5fjo1NnCg,70551
numpy/core/tests/data/umath-validation-set-log1p.csv,sha256=5hnT1xXhP9lCmLx_qZ3FMFrujTKdJS-5SZCsKX3yke0,61732
numpy/core/tests/data/umath-validation-set-log2.csv,sha256=ihQyfW16BQYldFbg2v7HErkm1efgGuops7tv7pwVCPI,70546
numpy/core/tests/data/umath-validation-set-sin.csv,sha256=aGuZ1Hr8i6PViQGA-UbAUjcXqAdcuAn9AqtFShv09Vg,59981
numpy/core/tests/data/umath-validation-set-sinh.csv,sha256=RI_UkXTgvcO2VAbFFZZSmR--RQbxSdQePc8gnfkLICE,61722
numpy/core/tests/data/umath-validation-set-tan.csv,sha256=H2Z3jO3nV6u0rXYPes3MnI4OSdaKcStfjjKoMiKsyFQ,61728
numpy/core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/core/tests/examples/cython/__pycache__/setup.cpython-38.pyc,,
numpy/core/tests/examples/cython/checks.pyx,sha256=IZwVIJAOuV8aAYZB-zMq6kif5Hqr8FkjY7eY6220Mg8,647
numpy/core/tests/examples/cython/setup.py,sha256=JvbvFHHdkZx_eajlwMTtqHijLD-TZwXCZlRySAe-y94,521
numpy/core/tests/examples/limited_api/__pycache__/setup.cpython-38.pyc,,
numpy/core/tests/examples/limited_api/limited_api.c,sha256=znFZtXQ0y9tOcovvZSjq9GrxBfx3st_s67Fru4owwUg,361
numpy/core/tests/examples/limited_api/setup.py,sha256=N7kqsVp4iIE20IebigEJUW3nW2F0l6Vthb5qNvKHBmM,457
numpy/core/tests/test__exceptions.py,sha256=PmFZh81gSxCUKkNs2wDaMICLM5uGp6lI92OynAd-KtM,2934
numpy/core/tests/test_abc.py,sha256=K04DCye4i4M5rAdoHxsaGHSKDdP4CyFRQuxP1FeE4OY,2274
numpy/core/tests/test_api.py,sha256=L1TYuX7d-Xxe91S01Hhu1bIM86A8Di4mdzN_5F8K7IE,23036
numpy/core/tests/test_argparse.py,sha256=W4ys2ZU9B-LQwcuY16KNb3QExGVhxV32yJ1pG5-zF7w,2039
numpy/core/tests/test_array_coercion.py,sha256=0IuGhmPehoyTfd2KVPPszJISgc6Bq0vt1T2dJOPh2jI,33104
numpy/core/tests/test_array_interface.py,sha256=_x4oKlzmwWA8pzZn6cUpGIDUSv16lM3f4SRTYmpRyiI,7812
numpy/core/tests/test_arraymethod.py,sha256=R6zHXSN2QxYsg1XtM-dMeLk3adhC8FEZTquerWxd2SA,3663
numpy/core/tests/test_arrayprint.py,sha256=IEhC4hkCYqGiJ190mO4gws8gnklzzrB11e1AFkUlFDw,38130
numpy/core/tests/test_casting_floatingpoint_errors.py,sha256=qC5FimCIGwxklVy5VeD9WNIBD2EZBos4JdiOy0e6H5I,5217
numpy/core/tests/test_casting_unittests.py,sha256=T6PCbjLs-75z0TSC2KDjkzZf_zXxizHPlvs577x50Hs,35117
numpy/core/tests/test_conversion_utils.py,sha256=4PJfmBFJ0N-K0Xqf1SBRxryyP0bfu5UF_p4el1YcPI4,6767
numpy/core/tests/test_cpu_dispatcher.py,sha256=vWLZWTYbMGanntWj1EfPcJMce0J4fizoPjmPVNGzIrs,1563
numpy/core/tests/test_cpu_features.py,sha256=35qPkNMzwA-oMYuZ3EikWUppT4KazKln0SSEE28W9RY,7354
numpy/core/tests/test_custom_dtypes.py,sha256=H0czaWzy9xtUnz7pY3Fr4w-q-xPIIwShoOcFD-qou-c,7878
numpy/core/tests/test_cython.py,sha256=vjEHsEL0wUUPVFGnm5KIYi2fgkYbzO5Ta4nO0I6XfAE,3760
numpy/core/tests/test_datetime.py,sha256=MCosMyjuD6p1V2xUD-FUjjapqrenvs_WwqpMfXhjUw4,118251
numpy/core/tests/test_defchararray.py,sha256=OC0eJo9aXf3-0dlT25_N5Isqcy0h0zw4F8TU3xegfhs,25279
numpy/core/tests/test_deprecations.py,sha256=q3OGd9amF9dRBnbWIrC75HQpFqlAGqOK-4bgueDAxHY,46687
numpy/core/tests/test_dlpack.py,sha256=DP5ExYsEwybnCsi-4A51vgC3Ts-z7N2M_22D1W2wQ-g,3626
numpy/core/tests/test_dtype.py,sha256=QCAY1Gi7EVUE6-O_lgWOOPlxGG84hGc9W4fydN3TirI,74390
numpy/core/tests/test_einsum.py,sha256=FmESS40SDhCCv7ELD8zYBs-AevVQsMaKIUeUqra3i8w,51313
numpy/core/tests/test_errstate.py,sha256=z5nadQobXL6skDWH_7cY_0FkCtG5-p3tRPE62wfwouo,2280
numpy/core/tests/test_extint128.py,sha256=b8vP_hDPltdHoxWBn2vAmOgjJe2NVW_vjnatdCOtxu8,5862
numpy/core/tests/test_function_base.py,sha256=FjbJ8JMRcbBxllRXRZhxbE6nlfP1HL8I4kfyW8RKr0s,14820
numpy/core/tests/test_getlimits.py,sha256=_svujcqDuqGHFkyhqPNBzclIzih4vcq9aBvuKncYUCY,5453
numpy/core/tests/test_half.py,sha256=IEg4aXzhhww-TPmuwY4HVD5heICoESznDdB4yLLW_uY,24789
numpy/core/tests/test_hashtable.py,sha256=6RRLuIzT9HAAJIOpLQijbv0PTdOEyWzZJ0mTH8lhJqs,1041
numpy/core/tests/test_indexerrors.py,sha256=iJu4EorQks1MmiwTV-fda-vd4HfCEwv9_Ba_rVea7xw,5263
numpy/core/tests/test_indexing.py,sha256=ZC1Kpjws0MgplYMNUAE2ssuGe7gkwsYeSMEFxa0n5E8,55734
numpy/core/tests/test_item_selection.py,sha256=8AukBkttHRVkRBujEDDSW0AN1i6FvRDeJO5Cot9teFc,3665
numpy/core/tests/test_limited_api.py,sha256=Op5yY8Mi9UzowMQKyXEAS37IL6tcztHMoNKUZPaFnMQ,1216
numpy/core/tests/test_longdouble.py,sha256=rUSdB2t2pyepIVWyaCL9YaCK_e8G64VI7HumbNKmzWg,13412
numpy/core/tests/test_machar.py,sha256=pauEGVLnT_k8tD0Meu_s_uxutPvSwpOPywlFoizehMg,1097
numpy/core/tests/test_mem_overlap.py,sha256=aDMv7dmbNwWDE3WJP-iG2cyg6Dg1Ifkcd33Ku_a3NUE,30020
numpy/core/tests/test_mem_policy.py,sha256=VHFEJchVxFLXk-ORIeTGFecERh4nyn9AQWwZ5PlXWLc,16373
numpy/core/tests/test_memmap.py,sha256=Q7LuH-2KfuOM5e5mmwD0dhg7b-IjMVSeNpy9WQjT6xc,7698
numpy/core/tests/test_multiarray.py,sha256=4egsHMYjs9zVvKIWopH_4ir8fUdKWewovFKULCA5gGA,380287
numpy/core/tests/test_nditer.py,sha256=1-4wAUBTwgD1SY5OrrAQGsvl4hpKOfRW339-j9SLOsg,132869
numpy/core/tests/test_nep50_promotions.py,sha256=c9V0Zs0Ik0dtRsvINI2gzzvmyPZaDvMT2e5UbyNEZg8,6642
numpy/core/tests/test_numeric.py,sha256=Ab658GG4OiyJuT4Yuajh7vwifLjFFGiRGDI9OhlCIHI,141274
numpy/core/tests/test_numerictypes.py,sha256=A5DkVA4Z8AUROjydb8ddB_rV6OktrwWqw6_gfDIcK58,21824
numpy/core/tests/test_overrides.py,sha256=FcRsHLJX1Q_Uaud2HVccFfBx8QfNu9jiqh11-maApV8,22726
numpy/core/tests/test_print.py,sha256=I3-R4iNbFjmVdl5xHPb9ezYb4zDfdXNfzVZt3Lz3bqU,6937
numpy/core/tests/test_protocols.py,sha256=Etu0M6T-xFJjAyXy2EcCH48Tbe5VRnZCySjMy0RhbPY,1212
numpy/core/tests/test_records.py,sha256=xYbtYTeGctOKfU49aEkfx47IMPuyh_YJ-UGU0PqhBr8,20789
numpy/core/tests/test_regression.py,sha256=qnF-HSYIAptlMTEo7Kuic4gE3dD4Zn3FKvQq-E9A9Z4,93801
numpy/core/tests/test_scalar_ctors.py,sha256=8bqWTbtclndxzw-FTkcn3nVJkR7yB1XKCSdp4c9OETs,6301
numpy/core/tests/test_scalar_methods.py,sha256=iQumHqpNJAY7Iqse72n45c8YDRHbgldiMxTKFDzj9B8,8084
numpy/core/tests/test_scalarbuffer.py,sha256=pt5NLIVo7EPY9Ih5FJZuy2saFEmyc5YPqg6eCVT2gq8,5741
numpy/core/tests/test_scalarinherit.py,sha256=85iI_IUAD8xEQ1MA8wUANv8-QDJlihJ7LO-gGdMG1Ok,2476
numpy/core/tests/test_scalarmath.py,sha256=X20ivaM13HW0JDKPK87Bw5zgTCbBdn5MbcSCVoTBBkU,43900
numpy/core/tests/test_scalarprint.py,sha256=5E5njBLwKYa3f3qnGu4XwaFToBUN4oUZ8F0nnf109PY,19076
numpy/core/tests/test_shape_base.py,sha256=2vx-tJVbVa4jPyZ6KEj0hJlVdLUtLTdrGpSZp0mZFBw,30485
numpy/core/tests/test_simd.py,sha256=UCEHT_eOjp1K9F1_TC4-AJ7zg-WmVRN7MyBzsbE6upQ,44838
numpy/core/tests/test_simd_module.py,sha256=mRWcx_FhTY2HSMTYMMH_Nrxg8rYPQ2YJ5ss693QKLjQ,3904
numpy/core/tests/test_strings.py,sha256=LMYNdCJOvhSAIVqnz8iD-L7PskpmHSSx46yHnx4hpNw,3934
numpy/core/tests/test_ufunc.py,sha256=NzgYvkZrS54P8RCiZD_pE4T5L2OqMiv8taoCGu5G7GM,119416
numpy/core/tests/test_umath.py,sha256=jFFntpd8f7BUpUZa_d7RCuRzprHfnhgdT-TCoPF2JWA,176908
numpy/core/tests/test_umath_accuracy.py,sha256=K18zKG1jqi1EWf89jN9NR9-GCzoN1VHBgA5CtHIMkdo,3972
numpy/core/tests/test_umath_complex.py,sha256=nTz9kH60eDG6Plq2iZ2KO394oAWkGrvGnae2P4JSOUI,23865
numpy/core/tests/test_unicode.py,sha256=CNjBKb4FuTnXVBU7-9GGqz-n3Zu6r_xTOghAq6JcSoo,13015
numpy/core/umath.py,sha256=IE9whDRUf3FOx3hdo6bGB0X_4OOJn_Wk6ajnbrc542A,2076
numpy/core/umath_tests.py,sha256=IuFDModusxI6j5Qk-VWYHRZDIE806dzvju0qYlvwmfY,402
numpy/ctypeslib.py,sha256=nvfW3iT0-DOVAsd7wbFuMd_aHVXaYAPudIouL7OYL1I,18007
numpy/ctypeslib.pyi,sha256=Xf1wbREr577XCOsZBICgycP3AjGGBV98cU0UAisFpYo,8213
numpy/distutils/__config__.py,sha256=YzmWcMBEO6UpS4N50wsDKf7FnXEGZprMJMp-SNe_ruQ,5250
numpy/distutils/__init__.py,sha256=sh1TV9_aW0YWvmHfBPtbZKCRcZTN6BnxKV-mIAG2vuY,2138
numpy/distutils/__init__.pyi,sha256=6KiQIH85pUXaIlow3KW06e1_ZJBocVY6lIGghNaW33A,123
numpy/distutils/__pycache__/__config__.cpython-38.pyc,,
numpy/distutils/__pycache__/__init__.cpython-38.pyc,,
numpy/distutils/__pycache__/_shell_utils.cpython-38.pyc,,
numpy/distutils/__pycache__/armccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/ccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/ccompiler_opt.cpython-38.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-38.pyc,,
numpy/distutils/__pycache__/core.cpython-38.pyc,,
numpy/distutils/__pycache__/cpuinfo.cpython-38.pyc,,
numpy/distutils/__pycache__/exec_command.cpython-38.pyc,,
numpy/distutils/__pycache__/extension.cpython-38.pyc,,
numpy/distutils/__pycache__/from_template.cpython-38.pyc,,
numpy/distutils/__pycache__/intelccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/lib2def.cpython-38.pyc,,
numpy/distutils/__pycache__/line_endings.cpython-38.pyc,,
numpy/distutils/__pycache__/log.cpython-38.pyc,,
numpy/distutils/__pycache__/mingw32ccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/misc_util.cpython-38.pyc,,
numpy/distutils/__pycache__/msvc9compiler.cpython-38.pyc,,
numpy/distutils/__pycache__/msvccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/npy_pkg_config.cpython-38.pyc,,
numpy/distutils/__pycache__/numpy_distribution.cpython-38.pyc,,
numpy/distutils/__pycache__/pathccompiler.cpython-38.pyc,,
numpy/distutils/__pycache__/setup.cpython-38.pyc,,
numpy/distutils/__pycache__/system_info.cpython-38.pyc,,
numpy/distutils/__pycache__/unixccompiler.cpython-38.pyc,,
numpy/distutils/_shell_utils.py,sha256=9pI0lXlRJxB22TPVBNUhWe7EnE-V6xIhMNQSR8LOw40,2704
numpy/distutils/armccompiler.py,sha256=6sKNp543q_4NafErHoFOPKz8R3YJR9soDCr1WeFr5Xk,988
numpy/distutils/ccompiler.py,sha256=LdP63PSeLc8hL6dXjzLSPVA218uQ-byaUp61Iub3KKU,28940
numpy/distutils/ccompiler_opt.py,sha256=bkf0MAOhYMKSbTx3HITOlU3ywUP5kGm6weO0JEm2VqA,102693
numpy/distutils/checks/cpu_asimd.c,sha256=Nit4NvYvo3XWtBKeV6rmIszdNLu9AY81sqMFCTkKXBE,845
numpy/distutils/checks/cpu_asimddp.c,sha256=bQP32IzQZANu9aFu3qkovLYJXKCm0bJ6srsO5Ho2GKI,448
numpy/distutils/checks/cpu_asimdfhm.c,sha256=xJjmEakgtmK9zlx2fIT6UZ4eZreLzdCoOVkkGPyzXFA,548
numpy/distutils/checks/cpu_asimdhp.c,sha256=0eTZ2E1Gyk3G5XfkpSN32yI9AC3SUwwFetyAOtEp5u4,394
numpy/distutils/checks/cpu_avx.c,sha256=69aCE28EArV-BmdFKhCA5djgNZAZtQg2zdea3VQD-co,799
numpy/distutils/checks/cpu_avx2.c,sha256=207hFoh4ojzMAPQ53ug_Y5qCFIgZ1e8SdI1-o2jzdB4,769
numpy/distutils/checks/cpu_avx512_clx.c,sha256=CfPjudkRZ9_xygLVOySSEjoAfkjjfu4ipkWK4uCahbU,864
numpy/distutils/checks/cpu_avx512_cnl.c,sha256=eKCPRk6p1B0bPAyOY0oWRKZMfa-c5g-skvJGGlG5I4Y,972
numpy/distutils/checks/cpu_avx512_icl.c,sha256=Zt8XOXZL85Ds5HvZlAwUVilT6mGbPU44Iir44ul6y2Y,1030
numpy/distutils/checks/cpu_avx512_knl.c,sha256=ikHHx87EfJ3-sjJoT5QeSIvmrHrO4oC9KJzCB-wp5BE,981
numpy/distutils/checks/cpu_avx512_knm.c,sha256=iVdJnZ5HY59XhUv4GzwqYRwz2E_jWJnk1uSz97MvxY0,1162
numpy/distutils/checks/cpu_avx512_skx.c,sha256=aOHpYdGPEx2FcnC7TKe9Nr7wQ0QWW20Uq3xRVSb4U90,1036
numpy/distutils/checks/cpu_avx512cd.c,sha256=zIl7AJXfxqnquZyHQvUAGr9M-vt62TIlylhdlrg-qkE,779
numpy/distutils/checks/cpu_avx512f.c,sha256=ibW0zon6XGYkdfnYETuPfREmE5OtO0HfuLTqXMsoqNA,775
numpy/distutils/checks/cpu_f16c.c,sha256=QxxI3vimUAkJ4eJ83va2mZzTJOk3yROI05fVY07H5To,890
numpy/distutils/checks/cpu_fma3.c,sha256=Cq0F_UpVJ4SYHcxXfaYoqHSYvWRJzZsB8IkOVl8K2ro,839
numpy/distutils/checks/cpu_fma4.c,sha256=Xy0YfVpQDCiFOOrCWH-RMkv7ms5ZAbSauwm2xEOT94o,314
numpy/distutils/checks/cpu_neon.c,sha256=I-R8DHE6JfzqmPpaF4NTdWxq5hEW-lJZPjSjW8ynFgo,619
numpy/distutils/checks/cpu_neon_fp16.c,sha256=6hdykX7cRL3ruejgK3bf_IXGQWol8OUITPEjvbz_1Hc,262
numpy/distutils/checks/cpu_neon_vfpv4.c,sha256=IY4cT03GTrzEZKLd7UInKtYC0DlgugFGGrkSTfwwvmU,630
numpy/distutils/checks/cpu_popcnt.c,sha256=Jkslm5DiuxbI-fBcCIgJjxjidm-Ps_yfAb_jJIZonE8,1081
numpy/distutils/checks/cpu_sse.c,sha256=XitLZu_qxXDINNpbfcUAL7iduT1I63HjNgtyE72SCEo,706
numpy/distutils/checks/cpu_sse2.c,sha256=OJpQzshqCS6Cp9X1I1yqh2ZPa0b2AoSmJn6HdApOzYk,717
numpy/distutils/checks/cpu_sse3.c,sha256=AmZkvTpXcoCAfVckXgvwloutI5CTHkwHJD86pYsntgk,709
numpy/distutils/checks/cpu_sse41.c,sha256=5GvpgxPcDL39iydUjKyS6WczOiXTs14KeXvlWVOr6LQ,695
numpy/distutils/checks/cpu_sse42.c,sha256=8eYzhquuXjRRGp3isTX0cNUV3pXATEPc-J-CDYTgTaU,712
numpy/distutils/checks/cpu_ssse3.c,sha256=QXWKRz5fGQv5bn282bJL4h_92-yqHFG_Gp5uLKvcA34,725
numpy/distutils/checks/cpu_vsx.c,sha256=gxWpdnkMeoaBCzlU_j56brB38KFo4ItFsjyiyo3YrKk,499
numpy/distutils/checks/cpu_vsx2.c,sha256=ycKoKXszrZkECYmonzKd7TgflpZyVc1Xq-gtJqyPKxs,276
numpy/distutils/checks/cpu_vsx3.c,sha256=pNA4w2odwo-mUfSnKnXl5SVY1z2nOxPZZcNC-L2YX1w,263
numpy/distutils/checks/cpu_vsx4.c,sha256=SROYYjVVc8gPlM4ERO--9Dk2MzvAecZzJxGKO_RTvPM,319
numpy/distutils/checks/cpu_vx.c,sha256=v1UZMj78POCN7sbFmW6N0GM_qQSUwHxiF15LQYADIUs,477
numpy/distutils/checks/cpu_vxe.c,sha256=1w8AvS6x8s_zTgcrDEGMKQmSqpJRX2NLprdSu_ibyjk,813
numpy/distutils/checks/cpu_vxe2.c,sha256=fY9P2fWo-b08dy4dmuNNc_xX3E0ruPRU9zLPzzgD-Z8,645
numpy/distutils/checks/cpu_xop.c,sha256=sPhOvyT-mdlbf6RlbZvMrslRwHnTFgP-HXLjueS7nwU,246
numpy/distutils/checks/extra_avx512bw_mask.c,sha256=7IRO24mpcuXRhm3refGWP91sy0e6RmSkmUQCWyxy__0,654
numpy/distutils/checks/extra_avx512dq_mask.c,sha256=jFtOKEtZl3iTpfbmFNB-u4DQNXXBST2toKCpxFIjEa0,520
numpy/distutils/checks/extra_avx512f_reduce.c,sha256=hIcCLMm_aXPfrhzCsoFdQiryIrntPqfDxz0tNOR985w,1636
numpy/distutils/checks/extra_vsx4_mma.c,sha256=-Pz_qQ55WfWmTWGTH0hvKrFTU2S2kjsVBfIK3w5sciE,520
numpy/distutils/checks/extra_vsx_asm.c,sha256=anSZskhKZImNk0lsSJJY_8GJQ0h3dDrkrmrGitlS7Fw,981
numpy/distutils/checks/test_flags.c,sha256=7rgVefVOKOBaefG_6riau_tT2IqI4MFrbSMGNFnqUBQ,17
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/__pycache__/__init__.cpython-38.pyc,,
numpy/distutils/command/__pycache__/autodist.cpython-38.pyc,,
numpy/distutils/command/__pycache__/bdist_rpm.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build_clib.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build_ext.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build_py.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build_scripts.cpython-38.pyc,,
numpy/distutils/command/__pycache__/build_src.cpython-38.pyc,,
numpy/distutils/command/__pycache__/config.cpython-38.pyc,,
numpy/distutils/command/__pycache__/config_compiler.cpython-38.pyc,,
numpy/distutils/command/__pycache__/develop.cpython-38.pyc,,
numpy/distutils/command/__pycache__/egg_info.cpython-38.pyc,,
numpy/distutils/command/__pycache__/install.cpython-38.pyc,,
numpy/distutils/command/__pycache__/install_clib.cpython-38.pyc,,
numpy/distutils/command/__pycache__/install_data.cpython-38.pyc,,
numpy/distutils/command/__pycache__/install_headers.cpython-38.pyc,,
numpy/distutils/command/__pycache__/sdist.cpython-38.pyc,,
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=6IbYgycGcCRrrWENUBqzAEhgtUhCGLnXNVnTCu3hxWc,2675
numpy/distutils/command/build_clib.py,sha256=dU-pKIebuQGlBvp93y2t4tfJlW7Z64cBgUzofvmg_Hk,19703
numpy/distutils/command/build_ext.py,sha256=x81eNfnyAdPHJcfjIovdQGcASDu5CgWCVT-z0irYL7M,32983
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=6hPzM9yEdLAs2U06qke9CRmrjbSRhNQ7yVj6kCBDtOw,31945
numpy/distutils/command/config.py,sha256=ID-DxagfYScPFQeyISNBqOQZOx875SOOI67tnymPPnY,21186
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=s_0Uf39tFoRLUBlkrRK4YlROZsLdkI-IsuiFFaiS3ls,3157
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=g5Ag2H3j3dz-qSwWegxiZSAnvAf0thYYFwfPVHf9rxc,944
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=hL0DDy7tMJ-5I-63BmkWkoLNX2c5GiQdQhj-XNG3Tm8,9865
numpy/distutils/core.py,sha256=4vvNzpLy_9AfakXgzC6OITRThJd4OdfSmrzxhYu49Fc,8388
numpy/distutils/cpuinfo.py,sha256=l5G7myXNwEOTynBIEitH-ghaF8Zw5pHQAjaYpPKNtTQ,23322
numpy/distutils/exec_command.py,sha256=ZnPon3CxIP1kCznPhTyPnCSOLS7sXAot4TeTPcqVQdw,10598
numpy/distutils/extension.py,sha256=gho-x1rzPK16ca8zakRKHvbZL4Gvp1VFTEToE2-2k4M,3675
numpy/distutils/fcompiler/__init__.py,sha256=-tPSEgDsj2e-Wtf_a5FiHz6oxHsml9l4bGkkROBbsLQ,41565
numpy/distutils/fcompiler/__pycache__/__init__.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/absoft.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/arm.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/compaq.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/environment.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/fujitsu.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/g95.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/gnu.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/hpux.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/ibm.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/intel.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/lahey.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/mips.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/nag.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/none.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/nv.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/pathf95.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/pg.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/sun.cpython-38.pyc,,
numpy/distutils/fcompiler/__pycache__/vast.cpython-38.pyc,,
numpy/distutils/fcompiler/absoft.py,sha256=4UKxvpWQIphSdi6vJb3qILML_zyM3K_m7ddhAMS5dBI,5655
numpy/distutils/fcompiler/arm.py,sha256=Bpftt3HnmJc3Iyt8-nwsNv86JqdFYK0JMwh3CC8nP_k,2161
numpy/distutils/fcompiler/compaq.py,sha256=yyReqFAq42dy1zscMAV0GqVaYW7Iao1HtAUpnv5XTec,4023
numpy/distutils/fcompiler/environment.py,sha256=PVS1al3wahDNnneNVSl1sQhMPfz2dUXaIDVJfy0wZBU,3168
numpy/distutils/fcompiler/fujitsu.py,sha256=g4dTLDFfLRAzhYayIwyHGBw1Y36DKtPOCYfA823ldNA,1379
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=6V_Ly_lwEEsfUDSz0vCDg86EhWlajHuyBy_ioLqKCdM,21057
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=HARaSCruJSuHbS3LruVESj2cndZUKTHiJZBn7NU4vo0,3636
numpy/distutils/fcompiler/intel.py,sha256=rlm017cVcyjIy1_s8a4lNHJ8ilo6TiYcIA_tuPojapY,6781
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=FpoDQWW_Y3Anm9-Psml-eNySCGzCp9_jP2Ej4_AwDy8,2864
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=jRyTlRE57lFJq659Xi-oUIy79nXYucyHawspR_D8c44,1613
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=cVcSFM9oR0KmO5AIb4Odw9OGslW6zvDGP88n-uEwxvQ,3696
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=BL-vypfI0GNJrTo-nKs445liTW2Qdfvrsu8RMjATL5A,8174
numpy/distutils/intelccompiler.py,sha256=77BDCj7_6Nnf92ZDeFQgA6aDKJGkzDQ7u0nuQGw1v8g,4345
numpy/distutils/lib2def.py,sha256=KnWZJaOsxmx57MEJxrsdPAlZbQBgu-27bSCjwO8cI6k,3746
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=a5-sPwcZei7kSP0ZQZH4tTrlRWHnL8jtzLCeUSPA_04,2990
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=xE2djO2LH-UCkqT0R_sOevJf0Lvd_qWQvty7kZGa88s,22668
numpy/distutils/misc_util.py,sha256=TaPLU0uT-bUg-OihMPSt_9UcTySNPl1evv7pPFvJkoA,91862
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=gqQySO-P6Egk3qgrNlyCF3ze_U47lIO9SrbFJrCQCO8,2723
numpy/distutils/npy_pkg_config.py,sha256=q-ASkO8wZ5HmiTEH_6gzO2bPV4i5dz3bTu4EuSxFQJM,13409
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/setup.py,sha256=zd5_kD7uTEOTgC8Fe93g9A_5AOBEySWwr-2OxHsBBEc,651
numpy/distutils/system_info.py,sha256=TAicwq6Mi8zUJ44c9Q9hc_-VVPMiNyvpSwiMj0vIZKs,114200
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_build_ext.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt_conf.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_exec_command.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_gnu.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_intel.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_nagfor.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_from_template.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_log.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_mingw32ccompiler.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_misc_util.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_npy_pkg_config.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_shell_utils.cpython-38.pyc,,
numpy/distutils/tests/__pycache__/test_system_info.cpython-38.pyc,,
numpy/distutils/tests/test_build_ext.py,sha256=qx3jyRQ1NoTiIeEZU8z48aE113fUdwmT7foyO00QugM,2843
numpy/distutils/tests/test_ccompiler_opt.py,sha256=7d7oX4q946jxQ3T9TyZUyqNQ_cg-wO7UM2sUc4rieUM,29571
numpy/distutils/tests/test_ccompiler_opt_conf.py,sha256=3KyqLepj3nC2C1UYm8nv1Ne5O6KtufD-7DlvAYJuvOo,6523
numpy/distutils/tests/test_exec_command.py,sha256=EVipBhoXEJjlSwtQRptWJC1LNJc6wfYzu_81V2jdAL8,7612
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_log.py,sha256=ylfdL0kBkbjj_Tgqx47UGykAtpE_mJkLndL40p11AYc,902
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=YKK2WrJqVJ5o71mWL5oP0l-EVQmqKlf3XU8y7co0KYc,3300
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=aKtyXpHEYARNsAq9q5SeVC0qqMfm1gzvlN6-nXOVlac,2193
numpy/distutils/tests/test_system_info.py,sha256=MAT0Q5MprI7PHsh4PNe4ZS2c5eRlQnFajC06hqg9A18,11332
numpy/distutils/unixccompiler.py,sha256=ED_e7yHVNj4oXMze6KY8TbPxjyvHDC6o4VNGAkFA5ZQ,5567
numpy/doc/__init__.py,sha256=llSbqjSXybPuXqt6WJFZhgYnscgYl4m1tUBy_LhfCE0,534
numpy/doc/__pycache__/__init__.cpython-38.pyc,,
numpy/doc/__pycache__/constants.cpython-38.pyc,,
numpy/doc/__pycache__/ufuncs.cpython-38.pyc,,
numpy/doc/constants.py,sha256=8jSZxoMlAwNxDbAdJQfiNvx5RgDDfp_ISjWKbpTqhsM,9567
numpy/doc/ufuncs.py,sha256=ERF8YNwda32wM_OH6-n56zECahjpH3bcGKv4gYA0txc,5494
numpy/dual.py,sha256=RgoFIabqn8Hu9lSRakjO1plfDdYBJQq_8Gn__rE8TqQ,2297
numpy/f2py/__init__.py,sha256=8Io4CMtUF1EHYeDMAZxkp_oN0zdrLMwcFcTq80IypvQ,5413
numpy/f2py/__init__.pyi,sha256=nveiZP_Gx4OrS0EnsO6QNzdW70W3GaXaqGYf2VZItlY,1150
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-38.pyc,,
numpy/f2py/__pycache__/__main__.cpython-38.pyc,,
numpy/f2py/__pycache__/__version__.cpython-38.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-38.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-38.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-38.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-38.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-38.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-38.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-38.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-38.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-38.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-38.pyc,,
numpy/f2py/__pycache__/rules.cpython-38.pyc,,
numpy/f2py/__pycache__/setup.cpython-38.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-38.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-38.pyc,,
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/auxfuncs.py,sha256=DpQ8OvUU7Bnm47GdH6Nl34G2Sq_fCyTxJTbwOZlHmxc,23593
numpy/f2py/capi_maps.py,sha256=te4Vozw3fAEzY9Wd99CTuF5e_pRO63Cs9bPUnD6nfzY,33888
numpy/f2py/cb_rules.py,sha256=kdb47almhK4Aw1b3Wrax7m92DmYK09a0lhxWndc_BnQ,25688
numpy/f2py/cfuncs.py,sha256=R1na4Zj8lIxL3eer3HbkN40srAsqkId1Kq4EBKzSslc,52881
numpy/f2py/common_rules.py,sha256=hq6x1xef_gQpNSq3XKPrhbZs_4UMsOZgP-5nWaHVpoo,5238
numpy/f2py/crackfortran.py,sha256=gzq4J_sBGCU4O8dssWaGoJyq9dNL6oAvX5iQMqjwqfM,142518
numpy/f2py/diagnose.py,sha256=LmGu6iZKr9WHfMZFIIEobZvRJd9Ktdqx0nYekWk4N7g,5384
numpy/f2py/f2py2e.py,sha256=OlvH6CE1XdoRcWK86mF7CktkJnEwJcyOyj9xy3SUnww,25330
numpy/f2py/f90mod_rules.py,sha256=YrUudoDH_7OQzKKQTeua3aDy2iY52sb1hgYqmlZoPcU,9700
numpy/f2py/func2subr.py,sha256=-ZYOBFBPGaqCKZTWiXOIWefySg980zD9906EaRkHyGQ,9757
numpy/f2py/rules.py,sha256=AQlm2CzBJRRQhPIwdTp8OCEEkdHj3l7HycGX5_L5f4U,64326
numpy/f2py/setup.py,sha256=AYA8aERgAo5gj7zt9IiYTnFK8qqjN2GzuxrfyuAJ_Qc,2406
numpy/f2py/src/fortranobject.c,sha256=uux-j3sBIgpjM7VJGkpLxa6jJWccxng8X-FQXLdN3Dc,47432
numpy/f2py/src/fortranobject.h,sha256=OpPpUroaM_DF3dZx_3j2rICdqbdu6eEm4OTeHm-xXIM,6008
numpy/f2py/symbolic.py,sha256=bjBuZoVCaGmF1bQ2llJXAtbk-lslHn7Sf8wL9Lnedyc,54514
numpy/f2py/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/f2py/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_compile_function.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_module_doc.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-38.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-38.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=meFXuH32wgcHdmO3pOy34qyjUjpMEVAVG0ThTF0sd3c,7529
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=-uz75kquU4wobaAPZ1DLKXJg6ySCZoDME1ce6YZ2q5Y,175
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=wDMoF7F7VFYdeocfTyWIh7noniEwExVb364HrhUSbSg,102
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=geH5qY3RmabRrjLG2WUpp2YNx99W7by9mVJBnpQRqwg,307
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/module_data/mod.mod,sha256=EkjrU7NTZrOH68yKrz6C_eyJMSFSxGgC2yMQT9Zscek,412
numpy/f2py/tests/src/module_data/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/scalar_string.f90,sha256=U1QqVgbF1DbxdFekRjchyDlFRPnXwzG72kuE8A44Za8,185
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=MbbSUQI5Enzq46KWFHRzQbY7q6ZHJH_9NRL-C9i13Wg,199
numpy/f2py/tests/test_abstract_interface.py,sha256=fpwfzU3PRqNEhWNSvkNu0eLoA6rzyFdNZ4NMIUR2hGI,857
numpy/f2py/tests/test_array_from_pyobj.py,sha256=P1NLvTkXSDnqK86L0Qdb0BgSriqmNleBFULFxMJ0QPk,24735
numpy/f2py/tests/test_assumed_shape.py,sha256=IyqJPGpGVv_RaRCwrko_793jLxJC1495tR9gAbmTlR8,1515
numpy/f2py/tests/test_block_docstring.py,sha256=0UP4PhKCiF6vgaczLt3tHijL4sbP6DIn6NMTVfukkOI,581
numpy/f2py/tests/test_callback.py,sha256=34l0UOqstCZtb07wvAY6FKmWDJgeI0qsMW5S56lFwws,6382
numpy/f2py/tests/test_character.py,sha256=TCPLd3nwPWikzEPVKDiGIiklgSpX-dPxbP6TXlT_hRE,21060
numpy/f2py/tests/test_common.py,sha256=x_GnxoBXNgij-zsl875va_fUf38uSWj6JyDePM359wY,602
numpy/f2py/tests/test_compile_function.py,sha256=-i-zIVJzI-H3mmikdW9cINNmJ35JbuTTlFq5Ld9kswY,4303
numpy/f2py/tests/test_crackfortran.py,sha256=SaoN2YER503TnAAxl7_9hRMpY3N3SaJi9MgTW_KjS18,10928
numpy/f2py/tests/test_docs.py,sha256=vfRa7D_I_GsaROqENe1XzhkivcBixEjbR8G_vByBEi4,1717
numpy/f2py/tests/test_f2cmap.py,sha256=bqUqqq1DJz9k2v2TnE9YKN13TS2l8kL1Yxw87J4jpVk,406
numpy/f2py/tests/test_f2py2e.py,sha256=-wtutKIscl5lSIuyBzcBzcSVBDKBTOj_xBNFhUoiZIA,21617
numpy/f2py/tests/test_kind.py,sha256=ShvLRQIimK1TLl1rkpSI3K7TDecWloq7-jZ-D1omEzs,873
numpy/f2py/tests/test_mixed.py,sha256=XmGaJC4Nf1OZfOxVX7TYp6KlaeZeb8ZytmiKIql3VvI,881
numpy/f2py/tests/test_module_doc.py,sha256=vYP-TM5D21z2igbKcR9WVNSOLSXQWyQ9yVa6l9W2k90,890
numpy/f2py/tests/test_parameter.py,sha256=E3SfDemPom95ZpBjBq59Z98ztghPf6biwLrU-kmc56M,4053
numpy/f2py/tests/test_quoted_character.py,sha256=0rwaANREvKwA0Rz65_B5S2xKy52kx7xzdijO5qFz4ac,470
numpy/f2py/tests/test_regression.py,sha256=A9TmLBcr9UMlA0Bj7bHq3FrblIgDZEBWBnyQIFbhgRo,2223
numpy/f2py/tests/test_return_character.py,sha256=TlDsBmECSXhrBynUEGBFv0J9K7WnesQe2FlHXaHYgMM,1538
numpy/f2py/tests/test_return_complex.py,sha256=KZJbNjttZwzaVxPJhg7a_SvQ_bxm_wyua_9UfoaDYzA,2462
numpy/f2py/tests/test_return_integer.py,sha256=dxOnJ1h2JJsFVCsfUTKfk2vVaJGz-nXUYffCV46xY34,1905
numpy/f2py/tests/test_return_logical.py,sha256=gPBO6zxmwek0fUIvCDgybiltiNqiMwaIqqsY2o0PXtg,2081
numpy/f2py/tests/test_return_real.py,sha256=IVMSccLG3FNLqjUkBpNojp618hTUY_0nwG1jMJkpYhE,3462
numpy/f2py/tests/test_semicolon_split.py,sha256=XRRffHS0K82Kls91xtLcSzAIoWpHHpii8ZaUkJYSSH4,1709
numpy/f2py/tests/test_size.py,sha256=Qy8KH9Co1IL6GbnDJ5IDGRPD1HKQ3HL6zXCkN2wpuUY,1209
numpy/f2py/tests/test_string.py,sha256=vSMQKo1SK4Y1xpgVw8iquHHH2kmelFsmphMMKYhnAaM,3062
numpy/f2py/tests/test_symbolic.py,sha256=Zk4h3WC2etMrIEyMrayPpGthpWfuS35Yz-4XzzGFcY4,18835
numpy/f2py/tests/test_value_attrspec.py,sha256=wxF8WE9BQR4vj3ooVH0Z8Tnkse6GIUbU1CoMeyeqjF8,337
numpy/f2py/tests/util.py,sha256=eAA5CNaEoDO2O-4Q3hixXniIUR5ldzpWsD7PTxppy_4,10864
numpy/f2py/use_rules.py,sha256=ROzvjl0-GUOkT3kJS5KkYq8PsFxGjebA6uPq9-CyZEQ,3700
numpy/fft/__init__.py,sha256=efF5_sqvdBfwIBHcEvkIb0a7YWZvR5oa4jh_aelUTpk,8387
numpy/fft/__init__.pyi,sha256=wMurHg76HViKOj8Nt6YveOJ-zt_a4DOGuDu2TRmshvY,579
numpy/fft/__pycache__/__init__.cpython-38.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-38.pyc,,
numpy/fft/__pycache__/helper.cpython-38.pyc,,
numpy/fft/__pycache__/setup.cpython-38.pyc,,
numpy/fft/_pocketfft.py,sha256=Eig0b3LbIFwSRFVcB6XHbBpAbDFIjO5gijB5_cY8_1o,54321
numpy/fft/_pocketfft.pyi,sha256=W-WyKPRKzA0JwHwaDhkJ7uMNeXxv1-n1-kvqnBdj94g,2479
numpy/fft/_pocketfft_internal.cp38-win_amd64.pyd,sha256=vZ9i6P1Sg7frjiBbPD6ze4SGNFAt6vj0FjZ5BXZziIU,111104
numpy/fft/helper.py,sha256=divqfKoOb0p-Zojqokhj3fQ5HzgWTigxDxZIQnN_TUQ,6375
numpy/fft/helper.pyi,sha256=sqSWM32SxhTzjUZkiAcwC0lkrI2pPcXRR0cJVjVxQfU,1223
numpy/fft/setup.py,sha256=-aF3b5s_eL6Jl0rS_jMFtFPzbYyT55FQE2SgblAawf0,750
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-38.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-38.pyc,,
numpy/fft/tests/test_helper.py,sha256=iopdl7-SHA5E1Ex13rYdceskgoDI5T8_Kg12LfqK1HA,6315
numpy/fft/tests/test_pocketfft.py,sha256=QouqHEUFE3Yxz2aPj_zZiq2HcW2SqNWh5f2Vjj6b_K4,13203
numpy/lib/__init__.py,sha256=7X4A8psH67j5Uiiq3rGpX0faXX1mPJkP4nMCbzrKt0w,2332
numpy/lib/__init__.pyi,sha256=QAbawYSE6XWhTkWtjEb2XUgn18a4kCvUrjqw6QWGe64,5841
numpy/lib/__pycache__/__init__.cpython-38.pyc,,
numpy/lib/__pycache__/_datasource.cpython-38.pyc,,
numpy/lib/__pycache__/_iotools.cpython-38.pyc,,
numpy/lib/__pycache__/_version.cpython-38.pyc,,
numpy/lib/__pycache__/arraypad.cpython-38.pyc,,
numpy/lib/__pycache__/arraysetops.cpython-38.pyc,,
numpy/lib/__pycache__/arrayterator.cpython-38.pyc,,
numpy/lib/__pycache__/format.cpython-38.pyc,,
numpy/lib/__pycache__/function_base.cpython-38.pyc,,
numpy/lib/__pycache__/histograms.cpython-38.pyc,,
numpy/lib/__pycache__/index_tricks.cpython-38.pyc,,
numpy/lib/__pycache__/mixins.cpython-38.pyc,,
numpy/lib/__pycache__/nanfunctions.cpython-38.pyc,,
numpy/lib/__pycache__/npyio.cpython-38.pyc,,
numpy/lib/__pycache__/polynomial.cpython-38.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-38.pyc,,
numpy/lib/__pycache__/scimath.cpython-38.pyc,,
numpy/lib/__pycache__/setup.cpython-38.pyc,,
numpy/lib/__pycache__/shape_base.cpython-38.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-38.pyc,,
numpy/lib/__pycache__/twodim_base.cpython-38.pyc,,
numpy/lib/__pycache__/type_check.cpython-38.pyc,,
numpy/lib/__pycache__/ufunclike.cpython-38.pyc,,
numpy/lib/__pycache__/user_array.cpython-38.pyc,,
numpy/lib/__pycache__/utils.cpython-38.pyc,,
numpy/lib/_datasource.py,sha256=jK47KlC6O0l6Ev2la0HB0GfOgih94O8DsNjjeHa-ZRg,23347
numpy/lib/_iotools.py,sha256=IBMRLRtNoYbqUUPO9tite0VQYbMG8J7SN2ZCNj3pbQE,31770
numpy/lib/_version.py,sha256=IjsC8gQRuFo_Ns0fSuF0BW7ndA3h9jQV5mClyWe9A8s,5010
numpy/lib/_version.pyi,sha256=rw_2q4KJ-dfpqJuj1e3PtVqK4Yh2FdJa5AHdE5IRaWM,650
numpy/lib/arraypad.py,sha256=fJcQvYXAOZm4dvac6OyKHuFVVXf6MtRkb3o0DdIloSc,32239
numpy/lib/arraypad.pyi,sha256=8z4FY3lJ1Xde2FHkoxmpz94aYDk5MDFoMvto8KALxvM,1813
numpy/lib/arraysetops.py,sha256=saP9Q02Td6b4ozUJp5LwbCNPBRPKj7cXUFNYwNC9aeA,34636
numpy/lib/arraysetops.pyi,sha256=WIGeglI8_gOw4ExXRj15S1qN8QE1BUSDlEyoR98IEl0,8697
numpy/lib/arrayterator.py,sha256=29pO5S0ciEZwt1402Q0-5cRbyKspV4tlPX1-m_D_Hgc,7282
numpy/lib/arrayterator.pyi,sha256=GlJgmZGbY7vomeNHcX6jvmc5hVtguq4-fYFsw3zj2Zs,1586
numpy/lib/format.py,sha256=rQUTap4oD3-PG7cN9sL48UIv3QswjldfrX253sqUb8I,35214
numpy/lib/format.pyi,sha256=dAlF-kNz-H-Vtn9H7Cs8J1l1xUkc3A2e7oWK1Qy17bs,770
numpy/lib/function_base.py,sha256=8DwFqFMDRkGt_00Y6mm1KGvjOQG_s-53UHSxaceigUc,190755
numpy/lib/function_base.pyi,sha256=IBsKaOFLLr1lGbozKo6yvC8gXC0BjKuNy8Sd_DchORA,17282
numpy/lib/histograms.py,sha256=L0r3w1ADZYBQA_6iktsZZAmIwv69E1qVRo15N8-sVDg,38768
numpy/lib/histograms.pyi,sha256=iNMKj6DEO-7r5G5bH2ZPTnivyre7Xy5T2y7h6Knurig,1042
numpy/lib/index_tricks.py,sha256=nY7AmvEnRM5WkkBa15_yJkBgb55mx7OnZsYK5JDc5jw,31965
numpy/lib/index_tricks.pyi,sha256=Pk4fxh8vJ2peFeBMJHqW-BXMPPF5lw0xsqikzEvHqso,4403
numpy/lib/mixins.py,sha256=j_Hfrfp6d6lJAVFlM9y2lu6HHsK_yDInyU74SJWsZDQ,7228
numpy/lib/mixins.pyi,sha256=311dfgGno6FFV1knGjcJob3GqML80FxBaYOTZuYnC_A,3191
numpy/lib/nanfunctions.py,sha256=YOxPhKUilvBsKv0a27Y4ilda8OeaRFYJuuixnwijUdk,67436
numpy/lib/nanfunctions.pyi,sha256=_KUODwVSfoDYVb8Qp2FDnLfmusNM690WpSVuMrFwvw8,644
numpy/lib/npyio.py,sha256=gBuVQTba_joBYSClECETSIxSVW_UIWDV1NvGxlFNV-A,100007
numpy/lib/npyio.pyi,sha256=TLkY7wEDZ8FqeTgb5oSh97oEVTB0NwU_b0FJfj5vscw,9943
numpy/lib/polynomial.py,sha256=n_VvTCSMEXtqe2acdsSPTY8jphjqPWpqIuy6I6TIOW4,45595
numpy/lib/polynomial.pyi,sha256=E-leT7vdwHnTkLa2CeQ4Ntnr6oNgVy89sUXQOW50qlU,7261
numpy/lib/recfunctions.py,sha256=w674lpwv9oL_Ju8ObHc8qWtAkhAVn5f-4zzS5JbZz0Q,57837
numpy/lib/scimath.py,sha256=mVVANvK9ChJ3myB9ktfnOrh936ilNey9Eq8StSOYzbc,15662
numpy/lib/scimath.pyi,sha256=bcW3wCbYG_cQpWyMAQ9dRY5JenhnGt8RiBjCTewaxag,2977
numpy/lib/setup.py,sha256=1s3corE4egZnDZ6I8au_mx7muRPgb3ZxL4Ko2SHt_2Q,417
numpy/lib/shape_base.py,sha256=VuBrY9VgA7IvQF4BQnDqLujCadihjqPhsFeZv62P-f0,40547
numpy/lib/shape_base.pyi,sha256=Qlc5aMrGspeHpvGB6Fxim7Hqos3EXG7vhrrzEocAv98,5399
numpy/lib/stride_tricks.py,sha256=fVtBjoR_zpOKBYsGkFPH8QtKBzpTo3kVi-01HW5tMzg,18458
numpy/lib/stride_tricks.pyi,sha256=papM2ENge9VNzvNApZ3a2VApgrIkytrzr_Oy-ia0iwM,1827
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_financial_expired.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-38.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-38.pyc,,
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=pTTVh8ezp-lwAK3fkgvdKU8Arp5NMKznVD-M6Ex_uA0,341
numpy/lib/tests/data/py3-objarr.npz,sha256=qQR0gS57e9ta16d_vCQjaaKM74gPdlwCPkp55P-qrdw,449
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=H6PZKQ0tY6r1bhrcLRKMjWdWop5P4Rj_SYvrU9ukDzc,10921
numpy/lib/tests/test__iotools.py,sha256=q44VFSi9VzWaf_dJ-MGBtYA7z7TFR0j0AF-rbzhLXoo,14096
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_arraypad.py,sha256=nQVNoyMB4e9zfTJ5ExoNB7JWIkuFEdf5Fv4rCb4Gizg,55622
numpy/lib/tests/test_arraysetops.py,sha256=OypEN5etwXLZpXAZ3399DxS3beQAWTiEs08VPGohID8,36856
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_financial_expired.py,sha256=D4d0h5E49b8wfIRr_mYdh_VfSU1cFS8VWy5NHc7nBYM,258
numpy/lib/tests/test_format.py,sha256=F3NNCJpI35oZDdyb8LLKTsf1LSqhKepyZY6qb5LQViM,41884
numpy/lib/tests/test_function_base.py,sha256=sE0BqLnL-jGzn8okEwRFGbZ-tsiI4_mT6S653DzkyZw,153323
numpy/lib/tests/test_histograms.py,sha256=_V7JgW55DNG-DjyvKDDqMM5tfRh_mJWH4tLpV370RBQ,33260
numpy/lib/tests/test_index_tricks.py,sha256=NhkCiQoGBliuNrpK3wOq4-g_Yh_He57v0A3DS9jEGsw,20807
numpy/lib/tests/test_io.py,sha256=t43JMpNMxM21NvKm9sOJTD4L59DufRYeft5I02kEOfM,109864
numpy/lib/tests/test_loadtxt.py,sha256=C5a7DsYN2s4pNxliGUSNsLWufR7Xvy54LRmTOWV6PeA,39353
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=Utx3__RWXrxYjR7voK6fcQqeH6RNvzH-b8Buq2k0R6E,47836
numpy/lib/tests/test_packbits.py,sha256=XpFIaL8mOWfzD3WQaxd6WrDFWh4Mc51EPXIOqxt3wS0,17922
numpy/lib/tests/test_polynomial.py,sha256=x2h1teRr7d-gcu17w0s3G4NlZt1jgOjKTiXP_7lPqVI,11698
numpy/lib/tests/test_recfunctions.py,sha256=Z6KErGv16nLHZxDV-thE5aVKrz1cvWrfKTsZ_yBG94o,42552
numpy/lib/tests/test_regression.py,sha256=NXKpFka0DPE7z0-DID-FGh0ITFXi8nEj6Pg_-uBcDIg,8504
numpy/lib/tests/test_shape_base.py,sha256=izet9YVTlhEEfaP_kN8mj-F2S45_i8z2XROg6B9mOI4,27544
numpy/lib/tests/test_stride_tricks.py,sha256=1zeBcvhltePbeE6SBoF4gomAYaZzwaHjvbWqcgI2JiY,23494
numpy/lib/tests/test_twodim_base.py,sha256=aYXmpm4syhD1I44-hQPLFLDcQovF1BcydK6Y2DE1JN8,19499
numpy/lib/tests/test_type_check.py,sha256=ffuA-ndaMsUb0IvPalBMwGkoukIP9OZVJXCuq299qB8,15597
numpy/lib/tests/test_ufunclike.py,sha256=rpZTDbKPBVg5-byY1SKhkukyxB2Lvonw15ZARPE3mc0,3382
numpy/lib/tests/test_utils.py,sha256=BS2FhBxekLaF2sQgjPtZVsyJFRdBrAmNl3CaquUpdjo,4814
numpy/lib/twodim_base.py,sha256=oNtxMT84QRIJ3ERu0CK3ysYU73i5d-j_3MPCRYP_RXo,32509
numpy/lib/twodim_base.pyi,sha256=NVoc6yoeJlKC0zKvUfu9qXi2e-MA7vex6DPqGzOlgmg,5609
numpy/lib/type_check.py,sha256=rWqKCYs3r3QyBd0x5eD0IRHHAj4KpDcee1FI6kcu6Q8,20666
numpy/lib/type_check.pyi,sha256=btZZOeVEcX7J7igi9d4bjgToV2CWoWPIl0ZDX9J84WU,5793
numpy/lib/ufunclike.py,sha256=snevUCqlqCQKAlzz5f8DdFXkAarEPafG8CHmzXMs-OI,8299
numpy/lib/ufunclike.pyi,sha256=iTi6kfrbWzcrKfAfChC7CY48U8CDVxghnb8wofcbpuw,1359
numpy/lib/user_array.py,sha256=5yqkyjCmUIASGNx2bt7_ZMWJQJszkbD1Kn06qqv7POA,8007
numpy/lib/utils.py,sha256=CpNOIRQ6tOx5iKiQFnfHQEh8tJK-_fPP5r0ZbKx03TY,37295
numpy/lib/utils.pyi,sha256=doryLj8MvJLMas-01JahMyivjINE_upXWK1bEJxzlHE,2451
numpy/linalg/__init__.py,sha256=EefgbxHkx8sLgXXLtpD8i8A_SCx-V4uz1Z_aQkmW6Ec,1893
numpy/linalg/__init__.pyi,sha256=94TdIMqJcc1OMeDpom2Zv-QxKL81BQ55Otcc-wyOUeo,650
numpy/linalg/__pycache__/__init__.cpython-38.pyc,,
numpy/linalg/__pycache__/linalg.cpython-38.pyc,,
numpy/linalg/__pycache__/setup.cpython-38.pyc,,
numpy/linalg/_umath_linalg.cp38-win_amd64.pyd,sha256=Z8kcGGy0PTyBWcoCQAZ33MAcxqGOG9yDXP5xockFNpA,106496
numpy/linalg/lapack_lite.cp38-win_amd64.pyd,sha256=alz7VSo4Mgd1fXcRqegN3EKxPjn2qgQS8VPfi_XVVZs,17920
numpy/linalg/linalg.py,sha256=29gVtacqRXVBCuvwZzpfTRwRooZgHtFlzkO_uIvoc_o,92146
numpy/linalg/linalg.pyi,sha256=wdXH5Nj0iFpelmYBu8ixLpXef-q5A5Y38DFLY-a51Zc,7722
numpy/linalg/setup.py,sha256=ofO7k17GmpCPtQVGjDfcGF48p8R3syd2is3iAa28pl4,3408
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-38.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-38.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=OiohxD_QnBQzwZDfoYTEauve2QjwsXX7wkRHz6kXEDk,79762
numpy/linalg/tests/test_regression.py,sha256=T0iQkRUhOoxIHHro5kyxU7GFRhN3pZov6UaJGXxtvu0,5745
numpy/ma/__init__.py,sha256=9i-au2uOZ_K9q2t9Ezc9nEAS74Y4TXQZMoP9601UitU,1458
numpy/ma/__init__.pyi,sha256=7-pSI7xHlMWrvMS_fKT5M683a8S9iqL7d6qYuYme8TQ,6320
numpy/ma/__pycache__/__init__.cpython-38.pyc,,
numpy/ma/__pycache__/bench.cpython-38.pyc,,
numpy/ma/__pycache__/core.cpython-38.pyc,,
numpy/ma/__pycache__/extras.cpython-38.pyc,,
numpy/ma/__pycache__/mrecords.cpython-38.pyc,,
numpy/ma/__pycache__/setup.cpython-38.pyc,,
numpy/ma/__pycache__/testutils.cpython-38.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-38.pyc,,
numpy/ma/bench.py,sha256=4HgDHbz_nCW4GmVLyjqk5FcgU7O_cqxNyCHufLwyyIY,4989
numpy/ma/core.py,sha256=dRVKs4uJTYQx73dpsA2mtKdhAsQAo3K5X65yks-JJrA,281047
numpy/ma/core.pyi,sha256=4Q1ytDEcdOVsxfIkQH1kD227dK0xKZLeoaBA2UlzC6U,14749
numpy/ma/extras.py,sha256=QUDqNdpvcLsdR1UL1xmVIBpl0bz42Y7TtILyzbvIF_k,63955
numpy/ma/extras.pyi,sha256=C_OGRvFlzoEsfwX6SjhaIWLqhsJgljhzRkkWy5D9mko,2731
numpy/ma/mrecords.py,sha256=zy-LVXMJnyDCbsBfWyFCC8Z2-y6ApeXw9OfQJNiiWZg,28015
numpy/ma/mrecords.pyi,sha256=nMx2BRyVzU_7AnAKrF3QoBwQH9TxxQYqBrrv6WhVI_I,2024
numpy/ma/setup.py,sha256=DCi5FtZTlkhR3ByJH5Sp5B962bfcWpaOjA-y7ueyoog,430
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-38.pyc,,
numpy/ma/tests/test_core.py,sha256=74TerM7g0rWsW0i4i7_JapXnv95xjHXT2TJm4taROZE,216528
numpy/ma/tests/test_deprecations.py,sha256=WurKSuN6hsXmWxRoxstdVBXcKCTvYxlYz-ntSkW6qKc,2650
numpy/ma/tests/test_extras.py,sha256=CpTtF6Ekj1lFILeTlcOdah1RSXbY0E0EjHElsW4_ifU,74779
numpy/ma/tests/test_mrecords.py,sha256=mY5kv1FEn9KTgm2bukb0F74HsoenEAoJlvp1zzRTSBs,20383
numpy/ma/tests/test_old_ma.py,sha256=UNQoiLfjTsW8quPQauNdhiwGKWpvo90Gty0Yio8NdKc,33576
numpy/ma/tests/test_regression.py,sha256=e8yRITxYfgiAzmBD3BH-u6AMWYmMARJSE93IKgVP3_w,3169
numpy/ma/tests/test_subclassing.py,sha256=r7ZXEjOSBgqGorcYHZvQ5PxniBJ0An45AxZgqV80lw4,17020
numpy/ma/testutils.py,sha256=5nMEDNCI9e5KWPGQqFhi2_HPHJO5mZ_XQaMXwE4Fhl4,10527
numpy/ma/timer_comparison.py,sha256=xhRDTkkqvVLvB5HeFKIQqGuicRerabKKX3VmBUGc4Zs,16101
numpy/matlib.py,sha256=lW1bZKbPVVim348f5rtVlPjLLAMVJaclCdiXG7hsFHM,10843
numpy/matrixlib/__init__.py,sha256=9-DMlmdLxOk5HSGJ20AuTjKkGZ3MUPHCFjhE6sb4NMo,253
numpy/matrixlib/__init__.pyi,sha256=w70nB0WHow4AVG5tw3Rl9zv4S2n63-68T8AhVJlNup0,267
numpy/matrixlib/__pycache__/__init__.cpython-38.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-38.pyc,,
numpy/matrixlib/__pycache__/setup.cpython-38.pyc,,
numpy/matrixlib/defmatrix.py,sha256=zDXeDEu5DcW3R5ijl_MjGzp9bXM36uhWm3s0hbMTzJc,31780
numpy/matrixlib/defmatrix.pyi,sha256=i7medmOD8aL6_PMJSiGSnWmld_YOxsoP67Kh-SR_QLo,467
numpy/matrixlib/setup.py,sha256=DEY5vWe-ReFP31junY0nZ7HwDpRIVuHLUtiTXL_Kr3A,438
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-38.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=nbY_HkwzoJbhYhACiEN-cZmR644sVJvMKWUcsANPayQ,15435
numpy/matrixlib/tests/test_interaction.py,sha256=C1YtIubO6Qh8RR-XONzo8Mle4bu4SvwsvBnB0x0Gy4g,12229
numpy/matrixlib/tests/test_masked_matrix.py,sha256=pJBlAnKEourHnqNk9b23j_traRp978pAF5uzdQrtuZU,9163
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=FgYV3hwkpO0qyshDzG7n1JfQ-kKwnSZnA68jJHS7TeM,958
numpy/polynomial/__init__.py,sha256=3X2b_24V61Nb5Zs00ITyX4XCfGvxiOjgbiC6-FlASOs,6966
numpy/polynomial/__init__.pyi,sha256=ng3gyC_49Isv2zjbUFrZOxRd0zltgfqz0EXXS2NLN8o,723
numpy/polynomial/__pycache__/__init__.cpython-38.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-38.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-38.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-38.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-38.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-38.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-38.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-38.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-38.pyc,,
numpy/polynomial/__pycache__/setup.cpython-38.pyc,,
numpy/polynomial/_polybase.py,sha256=jGIKqODre2212oaSuo1iLZNaU-VCgwh98zGWUq8uJBg,39875
numpy/polynomial/_polybase.pyi,sha256=PLK_DYLWFhBfrNcgFPUGdSYYuJKUNl0pOll_ZmlBXgk,2392
numpy/polynomial/chebyshev.py,sha256=Twc-ecKk25CNF7yM_yBIB3gEpXKiZUjfnNC1x0NO0GU,64596
numpy/polynomial/chebyshev.pyi,sha256=UKyHeff6dp8DV3J5e4-iT31lu4-pwIXSu_oy35AsQRU,1438
numpy/polynomial/hermite.py,sha256=HzyaK2GkgnbITmOHdMSLhA0ZL8EWnMU9btPG0aCWUzk,53935
numpy/polynomial/hermite.pyi,sha256=kpbjKXoOW1QSHFtPTxLx7LGtIY540CcpaBF_YTO1TVY,1263
numpy/polynomial/hermite_e.py,sha256=Lq36k03od-OhWsVq0fmfWi2TmTxQ_QGrTlmrQ4S-MS8,54055
numpy/polynomial/hermite_e.pyi,sha256=AP6dm9PsnEox2i5MyVbJRZQMd0mgaqw5IasrCFtPOoo,1284
numpy/polynomial/laguerre.py,sha256=pFBmKZSKlv9WH3cfOwnPBIM8R1ZouvOMwSGEB3tqom4,52227
numpy/polynomial/laguerre.pyi,sha256=v5RTMhLIRsGxx9vKAAs6WyQqBjxjNK-YrTFKEJcJFaw,1224
numpy/polynomial/legendre.py,sha256=fpjKkDF8Dr1ZYli-AV_nzYsUanA3qGw9N99UfOKbbVQ,52932
numpy/polynomial/legendre.pyi,sha256=IOzJVF0pQefwXsuBNhE7h5Ecib5SxKBJJ-Au2T4laU8,1224
numpy/polynomial/polynomial.py,sha256=dlAyKSlV3_DC2qnrJmbcSmaGtAkXiuaxqQUQGUxQ6oA,50372
numpy/polynomial/polynomial.pyi,sha256=w4ocQ2Wf3wQWHSO2gklfPS1JVq-56Q_XUCxLLcjIces,1173
numpy/polynomial/polyutils.py,sha256=o08Hwi6uhw092NN2Z82f-p-yLP8QcGq6vjP-RMW9yu0,24026
numpy/polynomial/polyutils.pyi,sha256=_06GfKjfZuDuX0bgXqxWkx54FazIbwUQlNWZYjnnZpM,275
numpy/polynomial/setup.py,sha256=3MP1VT1AVy8_MdlhErP-lS0Rq5fActYCkxYeHJU88Vg,383
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-38.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-38.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=TIoMhOcCJd7bAAs-LVNUToLFtYORWRU1H5CSwaPdySU,18931
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=v3ajjp0sg1o7njoLhbPftxaIWaxpY0pBp1suImZqJMw,19241
numpy/polynomial/tests/test_polynomial.py,sha256=fE6-pAr1z4P1InHWUmekx7E_Mqqi8lJR43nIwgG_qKc,21140
numpy/polynomial/tests/test_polyutils.py,sha256=AQZZzxBCymhT1MTv8zCF_NC-nP0d9ybMNebT2d8P3j0,3700
numpy/polynomial/tests/test_printing.py,sha256=u2Ky3dkZQB_H_pwF2dVZY4jYVDlXhJBTjtnoUIvI-vc,21055
numpy/polynomial/tests/test_symbol.py,sha256=JHjxWjLYw3aOdjoOPendZyoXDbQMJ9D4lPiHBkdjddg,5587
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=DmuNxZkaSCfwkSXlC6FmXFTNk2WO3evediDFOAeozto,2215
numpy/random/__pycache__/__init__.cpython-38.pyc,,
numpy/random/__pycache__/_pickle.cpython-38.pyc,,
numpy/random/__pycache__/setup.cpython-38.pyc,,
numpy/random/_bounded_integers.cp38-win_amd64.pyd,sha256=FkCIjbD3VXsuXyteE7CPIVILDs_yw0ipIC-N3OzdPnU,239616
numpy/random/_bounded_integers.pxd,sha256=ugYlh8FvGggHCjEaqgO4S_MeRcZg3mw40sDYEqx07QQ,1698
numpy/random/_common.cp38-win_amd64.pyd,sha256=vVHAD5x36kY2lJt_ffr1r8n53O4AHnpVKaHOHrHrvXU,177664
numpy/random/_common.pxd,sha256=sWH5N7y3Qd1ePQp-MMz6EHLLYTpgphCKi4pHYx8YqO0,4847
numpy/random/_examples/cffi/__pycache__/extending.cpython-38.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-38.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=Jb5SRj8As2P07PtoeuiZHZctaY0oAb065PXbG4-T8fI,1884
numpy/random/_examples/cython/__pycache__/setup.cpython-38.pyc,,
numpy/random/_examples/cython/extending.pyx,sha256=RmpxvFfGsAGZwCY78LWrfpa307NG7vrE64TIiIpKEA4,2368
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/setup.py,sha256=iEGHI_h4WH2bscwdj3lp0JYP2EtFGUnxBdbbHgfJgNc,1447
numpy/random/_examples/numba/__pycache__/extending.cpython-38.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-38.pyc,,
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=tU62JEW13VyNuBPhSpDWqd9W9ammHJCLv61apg90lMc,2101
numpy/random/_generator.cp38-win_amd64.pyd,sha256=765OVPwQU7an3N1sx2vWVtK-1RFVC48PJNImidOdHUE,655360
numpy/random/_generator.pyi,sha256=RH2CT75_ONncmv1FbT_0t21fZBAJjZovn0yfqLrNUdQ,22320
numpy/random/_mt19937.cp38-win_amd64.pyd,sha256=cvnd_6unqZvd9h9iMnCYMci0DLLGEVv9IhCzYfcrU80,69120
numpy/random/_mt19937.pyi,sha256=moJQemI0KlSzUyLJKy4d3lq6-yzWe-eKe0-ocybBjmY,746
numpy/random/_pcg64.cp38-win_amd64.pyd,sha256=jzyCsOMtzNxoWnLFOWy7II0rj2pFytDecZNoH_Qj-wE,73728
numpy/random/_pcg64.pyi,sha256=Q-QetvAEmjzguUzTFe9WyNjouYT4AdB3t4TP7Rv_h9A,1133
numpy/random/_philox.cp38-win_amd64.pyd,sha256=Zejd-GLs7S9jdvmRVcFPFLwcaIh0bW-jiknIxiL8qUg,61440
numpy/random/_philox.pyi,sha256=ON7UZsb8vsIHC8LnnEAe_pe5RNvs8AxY6QoyuGshArc,1014
numpy/random/_pickle.py,sha256=6dDxQu_mwdXdk1TXWi2-gYDLamILdfkB71oHl2Y2Wjc,2398
numpy/random/_sfc64.cp38-win_amd64.pyd,sha256=yXlcK7lbtUsy0zDXuUubMysoppD3Ergd_fMUAxdJaWc,44032
numpy/random/_sfc64.pyi,sha256=P4SSo1zmzgVONfY79sLS0fASLZJ4hMwFz97akUzXLzs,737
numpy/random/bit_generator.cp38-win_amd64.pyd,sha256=i8Hc00w2pPrAnRrv7zsWNppvdwr_pHgsci0vVGVuvkM,139264
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=ihfaoTX_uPGKtJDhPljMC4y5Nr98egq7HeAu9Xg7wn8,3496
numpy/random/c_distributions.pxd,sha256=N8O_29MDGDOyMqmSKoaoooo4OJRRerBwsBXLw7_4j54,6147
numpy/random/lib/npyrandom.lib,sha256=crYJ2esS0QCzL5gkEvWYCVcZ3ig46wrtEBu0OgyZKuI,143872
numpy/random/mtrand.cp38-win_amd64.pyd,sha256=vU3vQ53lXTxQjKhX0Hs-GyCkhk1PtH39Oqr_8VfegQk,564736
numpy/random/mtrand.pyi,sha256=PANHmIWnqSK5V52KattaZQgiaeZS9K4zbc5KqxXN2sc,20298
numpy/random/setup.py,sha256=cdwNfl1vPScX6kWqv5og-vlKt9f2aBL3mqC6IBXkWFw,6847
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-38.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-38.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-38.pyc,,
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/test_direct.py,sha256=NLlyQC2gDpSmJ2Vj_rvrKZImvDkZOYVNvtx-Rdb4LVk,16907
numpy/random/tests/test_extending.py,sha256=lxYmi5y7UMNz-3s1gZwZ32Odq6ByQFu5AfCzWEh5gfI,3775
numpy/random/tests/test_generator_mt19937.py,sha256=7GsafMvFIy8rpst5L7-0_sk1oFw7tI0UmThl0DdeSNs,117484
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=YVgW77VNLSG36rf7v2_BitcRGooHRKr5syd_PcOjkDo,5789
numpy/random/tests/test_random.py,sha256=xk3PCXI8qw6L87ZCudv4vz6N3lTRQ-1h0-mH5lloFds,71714
numpy/random/tests/test_randomstate.py,sha256=-2DWarx8V6_bEG270Jg2auyYQ8mb6iG4I9qudn0pfTQ,87009
numpy/random/tests/test_randomstate_regression.py,sha256=MgTKKmJlf_U9I_rmCRpIvPjPsq9ZMW1qGomnIcWr6cw,8133
numpy/random/tests/test_regression.py,sha256=QZc3xP9sPfihkUF7LAzW8zE4AaJ0nionJoYO3M6pAdk,5588
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=KggwK88hgKJyDscYQaODUCS7jLEk1QE9JW-jJRmvxig,29001
numpy/setup.py,sha256=LNAu_SzoMgaXtXCagj9GvCpT4mPnBlgXmgxJQ-P_WhU,1133
numpy/testing/__init__.py,sha256=dbctYSu6-tGZxwmmdypHs6RdJCyQdeQkaw_XE4_faso,695
numpy/testing/__init__.pyi,sha256=3zza7ae_vF-ayV-AJRRqYD0q0VC2CWOB1I10l4Nscj0,1859
numpy/testing/__pycache__/__init__.cpython-38.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-38.pyc,,
numpy/testing/__pycache__/setup.cpython-38.pyc,,
numpy/testing/__pycache__/utils.cpython-38.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-38.pyc,,
numpy/testing/_private/__pycache__/decorators.cpython-38.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-38.pyc,,
numpy/testing/_private/__pycache__/noseclasses.cpython-38.pyc,,
numpy/testing/_private/__pycache__/nosetester.cpython-38.pyc,,
numpy/testing/_private/__pycache__/parameterized.cpython-38.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-38.pyc,,
numpy/testing/_private/decorators.py,sha256=lKeZylqjrMV3zA_3IjBHX64mHNZ2Qo9PNFBeIQyzns4,11732
numpy/testing/_private/extbuild.py,sha256=isV_Qomx0d4ZaA83aE8z5JozC9snQ1T1Lz5TBQGkBbs,8067
numpy/testing/_private/noseclasses.py,sha256=OqMSWVZEg5GGgz8bsoDWKa3oxvXYoqPst6U423s8yBM,14880
numpy/testing/_private/nosetester.py,sha256=35S7suLWVzYBZ-zOt3ugthNZcMBCP7AjA-Z-4jqmtus,19980
numpy/testing/_private/parameterized.py,sha256=IDHScV_r5nxv6InuuXT9qfH_OuNhRcQUZ6DbuU3VYdI,16588
numpy/testing/_private/utils.py,sha256=5V6QhwvoeyObNSi4S9gGJzh2BAIAK1OlJTNdcOpUdwY,90513
numpy/testing/_private/utils.pyi,sha256=X_1RQpQyigKvuhwbyGeFkmvatf6uRZ44cIcn4AKgChc,10447
numpy/testing/print_coercion_tables.py,sha256=1fOhWxSwwHCF6QrX76PCjfHguignkTKCyvpRTq4Gcwo,6380
numpy/testing/setup.py,sha256=wQPOZ8LI1xqkSe7vfY0apJHJwEyJ490uSveFh1wmTuk,730
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/testing/tests/__pycache__/test_doctesting.cpython-38.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-38.pyc,,
numpy/testing/tests/test_doctesting.py,sha256=wUauOPx75yuJgIHNWlPCpF0EUIGKDI-nzlImCwGeYo0,1404
numpy/testing/tests/test_utils.py,sha256=hWQWn_2av1pM5xQogGYaiYyHlLkbN760FRnLXp8rpV4,58484
numpy/testing/utils.py,sha256=dJcf8XDd5szqpHy8tbPmXM43E4a1FQdc6HK-Lj_IQzk,1284
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/tests/__pycache__/test__all__.cpython-38.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-38.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-38.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-38.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-38.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-38.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-38.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-38.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-38.pyc,,
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_ctypeslib.py,sha256=a-lBRmW_gjLza_u0R1xhrzC3n_3R4MxCeLPBx8OS0Lo,12658
numpy/tests/test_lazyloading.py,sha256=Z01x6jxk94e2HPoHdlHBgmgHjm9tNDE09kuJmT-DYFo,1200
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_version.py,sha256=N6ithCsZfV6UW5lVVMIPZQ-Yht5WIFplUOTSLmwtUNw,1619
numpy/tests/test_public_api.py,sha256=9O-XaxJk8wgttS2XBfhpPR5RKNisgHxKzWjher12ZpI,16644
numpy/tests/test_reloading.py,sha256=iLpy0aMaCVIvGdBFXFmSbsyCR3eTSd3BJm34htq8RRw,2426
numpy/tests/test_scripts.py,sha256=6rZN5bnGpeR4vEjLBiKEUMXJiE2NVnbY1Q8xKPlOqA8,1692
numpy/tests/test_warnings.py,sha256=IMFVROBQqYZPibnHmwepGqEUQoBlDtdC8DlRulbMAd8,2354
numpy/typing/__init__.py,sha256=WIxbUYD8B-3grzzQVqfgYgipThc1NQSBpeNikv1Euaw,5409
numpy/typing/__pycache__/__init__.cpython-38.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-38.pyc,,
numpy/typing/__pycache__/setup.cpython-38.pyc,,
numpy/typing/mypy_plugin.py,sha256=VFr2TjB2BN2HQNAXEzxJSGgE1YLWGXR7okzjX4vKeFM,6676
numpy/typing/setup.py,sha256=NpNqwxDwSxWBInw-7TJHIqEf3scDAczZwmzGxI_Ftn0,385
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-38.pyc,,
numpy/typing/tests/__pycache__/test_generic_alias.cpython-38.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-38.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-38.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-38.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=W3Uqw-5lzAqlYUh3h9SBZqr4HC4xmRxMuNaQyRMVUPw,3971
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=xmYGgCnxTHZ0nv5C8y-uuPsOnsQKXQjahE7muldp5cM,1122
numpy/typing/tests/data/fail/array_like.pyi,sha256=MUIx6Oc5bJeebr-TC4FhZFXnX9pJ5gQDv8moHmPek10,471
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=tNk1nyxQq45G-87GS_QiTlNJSxP7kdItvBqzVD2bCKY,563
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=u1pNRrRUM8Q0o131lqGjvTQRy8-eHeazSvRArhnAyOo,494
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=VBOUHgafxOWJQ_tXLb0Phpu93FbLZIgo7mF1-EVUZ0c,535
numpy/typing/tests/data/fail/char.pyi,sha256=xCEKTpdp5Al-Qn-hxkp6i-Cgw7HEk53pt2XAzvpd58Y,2681
numpy/typing/tests/data/fail/chararray.pyi,sha256=_a695QkkSHZ9utlqUYwVUumC2QGhGxcZteB0iGlrFug,2358
numpy/typing/tests/data/fail/comparisons.pyi,sha256=VjriRnjoRGFUsyCheiGo1s5qErQ5ajQW7fCBltxp3Zc,915
numpy/typing/tests/data/fail/constants.pyi,sha256=esbeTGlTUSu3v3jMJ6GBZ_j7Q7RsyJRB47MaKNZaCOk,293
numpy/typing/tests/data/fail/datasource.pyi,sha256=B05CkL35mBUyKmuvi5MF3vTkZRwc0SOJo_r0cKVBBuA,410
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=3NXuWXIpQBjSTBthdEz70XWpqIUhmp13Pjgmxnxl_w8,752
numpy/typing/tests/data/fail/false_positives.pyi,sha256=TKmRWDjlfVP2rgZczUMXcm9l0maPLDf7bSBon4Xfakw,377
numpy/typing/tests/data/fail/flatiter.pyi,sha256=zVjvKxKlUr1T4PUWCZg1PsQEBmfLFtX5eH3Q50390oI,868
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=XoM1AY9Ys2_XyN-xEF9css9vV3FZsF78jsSecmIEwtQ,5752
numpy/typing/tests/data/fail/histograms.pyi,sha256=JteTXgK_kXD8UPdihMZ_T2VcM3rTBj6t-MMRP8UHvhw,379
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=63ADYRCVtf0Dapc2dJpYJZDSIXK3MhhW_1lG30d3-RY,523
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=A_fZIktpi_2Q9zanq-Hq0zdKvMXyCfE6PpgUQNYILHY,2134
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=CKOpJiyJzPfhyt01CwcqqCAbIJYD9TkYr7OEjRYU8is,942
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=UGzb3HzY1lUdiwLcT_myFfL-nWedrkhxDt9mt68eokQ,289
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=0vg0lxV-ax5bRjYPZh2h6--JjvCYQkZkGbrkckZ2DmM,670
numpy/typing/tests/data/fail/multiarray.pyi,sha256=I4uoKVR-gsRtM577IrbEZbjyL0iGpRFPIKk3udj1xYM,1748
numpy/typing/tests/data/fail/ndarray.pyi,sha256=2I4smD6MlUD23Xx8Rt1gCtjj_m-tI5JEma-Z0unrgas,416
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=ez2Wux0McE_cYgG4-6P5JgQCBV6-GthuhC9ywYdYAPY,1415
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=7E1zJ2SZIF0ldbEmjtA_Bp6cV4Q-cS4Op0BJN3Vi3rc,444
numpy/typing/tests/data/fail/npyio.pyi,sha256=V_rwbNzIzRv7a3ySSoaCcM1i2WWyoESwWzwr1w9C1Vw,810
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=gMj9jxXeI8nssH4B-8syNYXseb2NIkIXFHsWU7rRJJA,354
numpy/typing/tests/data/fail/random.pyi,sha256=d2JzeDW9mG4CKlF4S-foBmtB00y42toTDFcCw09bdKg,2891
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=Y5GCkuGb9qUDMbXrXGcG65W5R45Lo8ppVV75trzf6IY,3044
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/testing.pyi,sha256=O1nk5xnSvKn7aAHNi3mMLYIr75ym5WIT-BvZemEnayQ,1398
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=jPdRkTn8fm_YTnZwOUZ-yNYE2fJT5X5rp56rlhjjovw,936
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=V5R7wY_P7KWn4IRxbLx42b1YF3wbIYzHEMr9BC41JHE,754
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=wU7hNwN9SMRC8pMxOyDteWoCC4pDOdwfvk_ZCyhAY-c,700
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=TiIj3qjjbAgNR0IahyYUGXDTA8AlSJLIKhDrfyzAHFw,1388
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=I96g0czQxXry3qxXChQ3BMwJJc79W6HXgBvYiyRmSXI,179
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=-iSPpOqauIOyCLKYKr76J9dyKMTucyh61ZoABXEBWlk,364
numpy/typing/tests/data/mypy.ini,sha256=zLGw-qR5E8qtC06v7Oxlg-sRtRgFSWWgJ1CD7oopvwY,176
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-38.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-38.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=G88j1QuZwNp5DeCsZjXKRPpYVx60EVlBS-VSdZuB2-Q,8049
numpy/typing/tests/data/pass/array_constructors.py,sha256=lIy-MTR2YZZpVBTKndCEUHj-BtWGf21fN2yc2MVwSvE,2556
numpy/typing/tests/data/pass/array_like.py,sha256=mVIaUxN1HXw8Me_bK-TFFfRQRTPGj-TGakMG6NbCIso,957
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=zYz-ZNXpxjXDdo4fYWv_RQZq5af581dnaZrlu0-sSC8,1101
numpy/typing/tests/data/pass/comparisons.py,sha256=phjskmrmZ0bHTGLyNvo3mDuHCGR6I1OOj6S8ks71wRQ,3293
numpy/typing/tests/data/pass/dtype.py,sha256=7LeJ9EI_R2p15v-HmSRImS-wmWC1jwlgZykBuxBZCHg,1130
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=eM6RUBjVInsShXlblqq07-I0QwoST8n6g8WWuPSgYtA,4002
numpy/typing/tests/data/pass/index_tricks.py,sha256=Vn5iEuWlNdbr03zMEwAHvjBgI25-uCqRAJfUvRVWSp0,1556
numpy/typing/tests/data/pass/lib_utils.py,sha256=wHgHeuhfdv2cYi5_7xUv61wUnPEEmjc6xyFF6qj8wRY,445
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=7X2GZIjTiE-WUrBCKNi1FesDI_ENCc9RY_Lf3X2UVUE,1378
numpy/typing/tests/data/pass/mod.py,sha256=Intb9Ni_LlHhEka8kk81-601JCOl85jz_4_BQDA6iYI,1727
numpy/typing/tests/data/pass/modules.py,sha256=r6OC-qSjkLsR3uMpZVBahowrJp42i2t3LS7gCnuRIYo,638
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=-1iJDSvdD86k3yJCrWf1nouQrRHStf4cheiZ5OHFE78,1720
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=YHs6KN3AsmO14_Vr9KEIZTWY9WRW713zCU8KnJxV7sY,2900
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/numeric.py,sha256=8AiyBs48ADbckFLK2JYyuEpnGi7G59cpQh-BBtaCUxc,1580
numpy/typing/tests/data/pass/numerictypes.py,sha256=s9OFag9m9t407msIIXpKln04zcD-6yAva9XKG1AqqQs,1020
numpy/typing/tests/data/pass/random.py,sha256=sPsOvmCpLVr4_WVbEb11GS0mFdh0PQAknLTv0vxuP40,63380
numpy/typing/tests/data/pass/scalars.py,sha256=PeVMO9i7vD27bw5zK_V0CKF0up4Ed8OECOpmT3oQhRY,3727
numpy/typing/tests/data/pass/simple.py,sha256=vVNV6ewDjVGAgsCbYxPE-j1EKg-u3_vDeEBDtgwCMq0,2849
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=l1JiZe3VXYLzgvbuk42Mk6fd_nvLsc9aJ233W0_MLm0,1170
numpy/typing/tests/data/pass/ufunclike.py,sha256=vhQJlqPAZegEyC882iIU0uCqoA7ijTOZP1Vij-MrgEI,1085
numpy/typing/tests/data/pass/ufuncs.py,sha256=xGuFo9gOvT04Pj7iNOhRIAIj_NHSTykIFyGV2Oj_0yA,479
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=84SQc0-Le9MIy9rC9pQ4JchXsaaRNY1sqKwxxEaMNuE,156
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=apG__WZKDe4Mc4lBTd3B9bR7X0kzrv7JP_DVI9sCC7c,21331
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=ciYLGCbC5atB1pxwmoZ3FxbCjb7h45NGdLhmVrPhCbE,12036
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=2Q2DxXCxLTbtu4jc4imoBNTho-KGCtdaBHT9neJs0rE,716
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=qa7LG2RcAbA5JC44_Z_t3CoIVGXy3qECmtwmRTmH-Ik,706
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=CfzXbyGl-NzFF27dTED8PGBgVdFbcXkatslHTrqbrtM,4731
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=mW99oYiREOyE_5VTFn1YTRIBMBaC8bjj80e_SHUkf0k,1152
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=e2OsUfoTxTdPdVZKJGeIP29jIdN01Nij1ESBsSsvQJU,3738
numpy/typing/tests/data/reveal/char.pyi,sha256=8xKiZ8vtqiO6RSUU2EDBGKlJcbvA3kfpEqsGTfS935s,8194
numpy/typing/tests/data/reveal/chararray.pyi,sha256=5VSe6nPmgwgYGYWPp3CchJyf52PcVVxlb5NP0qHWx_M,6444
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=oMQzX9nkPNXCZWpgG0ECRW81rmb5KnchHgrLiBHqP-Y,8279
numpy/typing/tests/data/reveal/constants.pyi,sha256=F1YXi9jshwurwm6FcdTc1ofgHR-miMirBAwJb_rcxbw,1992
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=6IAsgR0BZlQ7334j4xuqLOA9lyaN6zG0TPtvieBpdqc,5194
numpy/typing/tests/data/reveal/datasource.pyi,sha256=K4XZlEsYAbbzpD3KkkECzLH1fjategKa4PCwF1N8o3g,578
numpy/typing/tests/data/reveal/dtype.pyi,sha256=PEhDiRQ8I5Y10Z5US_NNQZfE9wqkhKMjFwIyvwYKG78,2864
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=9U2f4nBXzANTrJvFQ1LB8yNuLpuEEmV28Qx9RKHhQP4,2110
numpy/typing/tests/data/reveal/emath.pyi,sha256=EjCPw2_u9PHiNGTbd0VUleysUqgbDGeo_rapTtNUU7Y,2590
numpy/typing/tests/data/reveal/false_positives.pyi,sha256=TexdGVvcIT9_ol2ldT0xp4ASh7JYXd-FdGvV6yAVX4c,359
numpy/typing/tests/data/reveal/fft.pyi,sha256=A41nHw8LDzRXbhFLOQoQRN69S_cizvIGZA0t507votQ,1887
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=FbaA9105h03voSCDhak_2KkxHE8ZTMX00LAL-3vdsNs,842
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=omVO5Fw9iSoP9m8owIRLgJOkiz-Hf_y6lWFP1Y2MK5k,13928
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=dL9PAvLQbeWovNaqcppC1qaTtkoikjHCwILOmwGnys4,1594
numpy/typing/tests/data/reveal/histograms.pyi,sha256=zM4XlZjWTgqEJRg6ytcSdG7_i-2DEasyMPw_CAyvznE,1410
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=MlyUWGIsDdUMbbjBvmZ5urvnOJB06thxnHDP3yCfnLg,3547
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=bMFm-IK9aKkz_OMCP34zcdBhMJEPL5haTSdedrIrr_E,9317
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=CGaEX0DcuNd7whDr_n6xKUxgSwg3xP2SQ07ctMjVwaE,6464
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=hHc74Qzgc0sx-tSDd0UK4NeWsjzBoc19OGl0tBBq7Q8,947
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=3uI1jeWbiyC2CEQ5-eqDkUZmb9871IywI6ZjetQUiUY,623
numpy/typing/tests/data/reveal/linalg.pyi,sha256=9FdN_kribCt6Pog1Zv4De1pTldTj8nlF83S4Yrk5LhQ,6486
numpy/typing/tests/data/reveal/matrix.pyi,sha256=88U9V-ZV3U603UcCSbm_pYj_AalrkE41Nz1Fqjdhs0M,3102
numpy/typing/tests/data/reveal/memmap.pyi,sha256=FaZG4pUGuwYprfYGjJh___DhP9iqkKWde4Ohu2oBdZA,773
numpy/typing/tests/data/reveal/mod.pyi,sha256=irO1YSd5eF5VbFnOrYe1webn6Z-QtWtwE387SB8WHes,6136
numpy/typing/tests/data/reveal/modules.pyi,sha256=wmqhjyy7bQ_wkmyuuktHG_mTKY-ARCpmz8WNHOnkhdQ,1957
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=3iNrvHD2Fgv5sGlwLyGHn1Mmj0Cj4nRPenVgf7_VgK0,5814
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=XoIdrl6HtWMd9TbtGljdUeGWbTnVEviTh5ugCCl-Fmc,521
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=DZgkD8FQ0SGNVCoBHvDbRad2Xe_8_hLMBcTYzSWME8k,1964
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=5oC4mIuS2JMmmdwq96hmmbrpUXNrbS9WpITyrvfB8vY,8017
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=-ITCn7JR2GFXdv8n_bJwFZCsVUCxP7BNAjtLM7ry43Y,939
numpy/typing/tests/data/reveal/nditer.pyi,sha256=yM7JnrOFG-8eDNcrJX-RSS6hTANKZVLK7SYw5QKGZfE,2113
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=7N3Bwm8ut6kFzXsSMZXFenKpIzMwgqc9pGjWTi06QCA,674
numpy/typing/tests/data/reveal/npyio.pyi,sha256=DPqyaSd3D1fuTjp7O_6pt1KsniRBIixc1pPW4KtG3po,4556
numpy/typing/tests/data/reveal/numeric.pyi,sha256=a3SKVGUfZocx65sTiaQuyQ7nOp23xXrqkhN4hZybR28,6935
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=XJ9rx-lkP546MK3SVH3fVPEphVbjq5T2g_25lik7T84,1828
numpy/typing/tests/data/reveal/random.pyi,sha256=L7nNXNrYNqGmfH_5TIjWEun0vTAF7jkH6fIjrhp8XII,131052
numpy/typing/tests/data/reveal/rec.pyi,sha256=Udb8qEAJbPeujAExTHUDX-QYK4JWU2PZfn1_AaDQm4M,3508
numpy/typing/tests/data/reveal/scalars.pyi,sha256=hPIXgS7Qi9aYPx0NZoJIFAftR1dkN37q0HV-5UODSfU,5555
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=cASKYHfJXDLTzZP_BGVe2aaLaO2Id87apHp3hT9ZtGE,2689
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=N84pbKt3mia0RvaUybnG0DajzD06KzGdx3KWgS52dMg,1591
numpy/typing/tests/data/reveal/testing.pyi,sha256=O7d3jBfsFo624u4XgyrFLPj2rLFwV_BkDcdDIGgAtdI,9198
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=IckHF7P1dkoasCOvT0Z-xkAi7eBojlnkKxjU73CzftE,3399
numpy/typing/tests/data/reveal/type_check.pyi,sha256=2E7-WbhYNkg9pbzIsOk7TZeNo9Vn5IgXERkF0n8Qt9g,3104
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=BhpX7d0FgYNw4IxJLIwWiW2Cmxg-Y2-vmnmc3o-Hbsc,1329
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=Hs7ur6h5ULgo3uo8nyVTGbt-kGMGi_SSpkoEWZwK8kM,1348
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=4kVZPNsH_tFX8J_86jjswqYLugfmQFTOQCY4_Iaue0w,2987
numpy/typing/tests/data/reveal/version.pyi,sha256=sDVJ6IuDtzgObpzUOzOBUzDVAvY1zKgKFe1OYratqu0,321
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=ihpczQWLcIA9i6pVFwCVGU3pgc6AltQfrl6Fx5XplBo,429
numpy/typing/tests/test_generic_alias.py,sha256=0s5bCDuNi4EcjPZnrFGeU_MOIikGyWEYqvf3QWGL_Gs,7218
numpy/typing/tests/test_isfile.py,sha256=KEPwqJBkXEbMRPCtSxFYULfPNipG5dyY2BRYwPI4Lu8,842
numpy/typing/tests/test_runtime.py,sha256=US5QFpCkTDCgLkvdhBN8HOP8eAMHg5dOp_o-Tl4v-HI,3488
numpy/typing/tests/test_typing.py,sha256=TLqNy-StRZdGA3_EUW_CvzJAqQtMTpxpNJ4iWqtvvis,15767
numpy/version.py,sha256=Ph0IYh9ExfFLZ1azhCwkO5pQGfNk_5ICpLaQ4Mfl3vU,490
