#!/usr/bin/env python3
"""
测试批量导出功能
"""

import requests
import json

def test_batch_export():
    """测试批量导出功能"""
    
    # 测试URL
    url = "http://xiaoyuanst.com/financial/vouchers/batch-export"
    
    # 测试数据
    test_data = {
        "voucher_ids": [1, 2, 3],  # 假设的凭证ID
        "format": "excel"
    }
    
    # 测试JSON请求
    print("测试JSON请求...")
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Test-Client/1.0'
            },
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ JSON请求成功")
            print(f"文件大小: {len(response.content)} bytes")
        else:
            print("❌ JSON请求失败")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ JSON请求异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试表单请求
    print("测试表单请求...")
    try:
        form_data = {
            'voucher_ids': json.dumps(test_data['voucher_ids']),
            'format': test_data['format']
        }
        
        response = requests.post(
            url,
            data=form_data,
            headers={
                'User-Agent': 'Test-Client/1.0'
            },
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 表单请求成功")
            print(f"文件大小: {len(response.content)} bytes")
        else:
            print("❌ 表单请求失败")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 表单请求异常: {e}")

if __name__ == "__main__":
    test_batch_export()
