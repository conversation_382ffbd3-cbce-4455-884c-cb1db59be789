2025-06-21 12:29:34,139 INFO: 应用启动 - PID: 8316 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-21 12:32:04,869 ERROR: 删除财务凭证失败: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]DELETE 语句与 REFERENCE 约束"FK_stock_ins_voucher"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.stock_ins", column \'voucher_id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: DELETE FROM financial_vouchers WHERE financial_vouchers.id = ?]
[parameters: (41,)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:546]
2025-06-21 12:34:36,486 ERROR: 删除财务凭证失败: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]DELETE 语句与 REFERENCE 约束"FK_stock_ins_voucher"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.stock_ins", column \'voucher_id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: DELETE FROM financial_vouchers WHERE financial_vouchers.id = ?]
[parameters: (36,)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:546]
2025-06-21 12:34:44,050 ERROR: 删除财务凭证失败: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]DELETE 语句与 REFERENCE 约束"FK_stock_ins_voucher"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.stock_ins", column \'voucher_id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: DELETE FROM financial_vouchers WHERE financial_vouchers.id = ?]
[parameters: (36,)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:546]
