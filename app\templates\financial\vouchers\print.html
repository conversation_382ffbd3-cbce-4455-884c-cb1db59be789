<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务凭证打印 - {{ voucher.voucher_number }}</title>
    
    <!-- 使用统一的用友主题样式（包含打印样式） -->
    <link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
    
    <style>
        /* 用友标准A4横向打印样式 */
        body {
            font-family: 'SimSun', '宋体', serif;
            font-size: 12px;
            line-height: 1.3;
            margin: 0;
            padding: 15px;
            background: white;
            color: #000;
        }

        .voucher-container {
            width: 277mm; /* A4横向宽度减去边距 */
            height: 190mm; /* A4横向高度减去边距 */
            margin: 0 auto;
            background: white;
            padding: 10mm;
            box-sizing: border-box;
            position: relative;
        }

        /* 用友标准凭证头部 */
        .voucher-header {
            text-align: center;
            margin-bottom: 15px;
            position: relative;
        }

        .voucher-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .company-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }

        /* 用友标准信息行布局 */
        .voucher-info-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 13px;
            border: 1px solid #000;
            padding: 8px 12px;
            background: #f9f9f9;
        }

        .voucher-info-left,
        .voucher-info-center,
        .voucher-info-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: nowrap;
        }

        .info-item label {
            font-weight: bold;
            min-width: 50px;
        }

        .info-item .value {
            border-bottom: 1px solid #000;
            min-width: 80px;
            padding: 1px 8px;
            text-align: center;
        }
        
        /* 用友标准凭证表格 */
        .voucher-details {
            margin: 10px 0;
            flex: 1;
        }

        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            font-size: 12px;
            table-layout: fixed;
        }

        .voucher-table th {
            border: 1px solid #000;
            padding: 6px 4px;
            text-align: center;
            vertical-align: middle;
            background: #f8f8f8;
            font-weight: bold;
            height: 25px;
        }

        .voucher-table td {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: left;
            vertical-align: top;
            min-height: 22px;
            word-wrap: break-word;
            word-break: break-all;
        }

        /* 摘要列支持换行 */
        .col-summary {
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
        }

        /* 摘要单元格 */
        .summary-cell {
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
            max-width: 200px;
            padding: 6px 8px;
        }

        /* 用友标准列宽 */
        .col-sequence { width: 60px; min-width: 60px; max-width: 60px; }
        .col-summary { width: 25%; }
        .col-subject { width: 32%; }
        .col-debit { width: 17.5%; }
        .col-credit { width: 17.5%; }

        .voucher-table .amount {
            text-align: right;
            font-family: 'Times New Roman', 'Arial', monospace;
            padding-right: 20px;
            position: relative;
        }

        .voucher-table .amount::after {
            content: '元';
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #666;
            font-family: 'SimSun', '宋体', serif;
        }

        .voucher-table .total-row {
            background: #f0f0f0;
            font-weight: bold;
            border-top: 2px solid #000;
        }

        .voucher-table .total-row td {
            border-top: 2px solid #000;
        }
        
        /* 用友标准底部信息区 */
        .voucher-bottom {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .amount-chinese-section {
            display: flex;
            align-items: center;
            font-size: 12px;
            border: 1px solid #000;
            padding: 6px 10px;
            background: #f9f9f9;
        }

        .amount-chinese-label {
            font-weight: bold;
            margin-right: 10px;
            min-width: 80px;
        }

        .amount-chinese-value {
            flex: 1;
            border-bottom: 1px solid #000;
            padding: 2px 8px;
            min-height: 18px;
        }

        .attachment-section {
            display: flex;
            align-items: center;
            font-size: 12px;
            gap: 15px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .attachment-value {
            border-bottom: 1px solid #000;
            min-width: 40px;
            padding: 1px 8px;
            text-align: center;
        }

        /* 用友标准签名区 */
        .voucher-signature {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 10px 0;
            border-top: 1px solid #000;
        }

        .signature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            min-width: 120px;
            flex: 1;
        }

        .signature-label {
            font-weight: bold;
            white-space: nowrap;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            flex: 1;
            height: 25px;
            display: block;
            text-align: center;
            line-height: 25px;
            min-width: 60px;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
        
        .print-btn.landscape {
            background: #28a745;
        }
        
        .print-btn.landscape:hover {
            background: #1e7e34;
        }
        
        /* 用友标准打印样式 */
        @media print {
            .print-controls {
                display: none !important;
            }

            body {
                margin: 0;
                padding: 0;
                background: white;
            }

            .voucher-container {
                box-shadow: none;
                margin: 0;
                padding: 15mm;
                width: 100%;
                height: 100%;
                max-width: none;
                max-height: none;
            }

            @page {
                size: A4 landscape;
                margin: 10mm;
            }

            /* 确保打印时字体清晰 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .voucher-table {
                page-break-inside: avoid;
            }
        }

        /* 屏幕预览样式 */
        @media screen {
            .voucher-container {
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body class="voucher-print">
    <!-- 打印控制按钮 -->
    {% if not auto_print %}
    <div class="print-controls no-print">
        <button class="print-btn landscape" onclick="window.print()">
            <i class="fas fa-print"></i> 打印凭证（A4横向）
        </button>
        {% if is_preview %}
        <button class="print-btn" onclick="openDirectPrint()">
            <i class="fas fa-print"></i> 直接打印
        </button>
        {% endif %}
        <button class="print-btn" onclick="window.close()">
            <i class="fas fa-times"></i> 关闭
        </button>
        <a href="{{ url_for('financial.export_voucher_pdf', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-pdf"></i> 导出PDF
        </a>
        <a href="{{ url_for('financial.export_voucher_excel', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-excel"></i> 导出Excel
        </a>
    </div>
    {% endif %}

    <!-- 用友标准凭证内容 -->
    <div class="voucher-container">
        <!-- 凭证标题 -->
        <div class="voucher-header">
            <div class="voucher-title">记账凭证</div>
            <div class="company-name">{{ user_area.name }}</div>
        </div>

        <!-- 用友标准信息栏 -->
        <div class="voucher-info-section">
            <div class="voucher-info-left">
                <div class="info-item">
                    <label>凭证字:</label>
                    <span class="value">{{ voucher.voucher_type }}</span>
                </div>
                <div class="info-item">
                    <label>号:</label>
                    <span class="value">{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}</span>
                </div>
            </div>
            <div class="voucher-info-center">
                <div class="info-item">
                    <label>日期:</label>
                    <span class="value">{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</span>
                </div>
            </div>
            <div class="voucher-info-right">
                <div class="info-item">
                    <label>附件:</label>
                    <span class="value">{{ voucher.attachment_count or 0 }}</span><span style="margin-left: 2px;">张</span>
                </div>
            </div>
        </div>

        <!-- 用友标准凭证明细表格 -->
        <div class="voucher-details">
            <table class="voucher-table">
                <thead>
                    <tr>
                        <th class="col-sequence">序号</th>
                        <th class="col-summary">摘要</th>
                        <th class="col-subject">会计科目</th>
                        <th class="col-debit">借方金额</th>
                        <th class="col-credit">贷方金额</th>
                    </tr>
                </thead>
                <tbody>
                    {% set total_debit = 0 %}
                    {% set total_credit = 0 %}
                    {% set ns = namespace(row_count=0) %}

                    {% for detail in details %}
                    {% if detail.summary or detail.subject_id or detail.debit_amount > 0 or detail.credit_amount > 0 %}
                    {% set ns.row_count = ns.row_count + 1 %}
                    <tr>
                        <td style="text-align: center;">{{ ns.row_count }}</td>
                        <td class="summary-cell">{{ detail.summary or voucher.summary or '' }}</td>
                        <td>{{ detail.subject.code }} {{ detail.subject.name }}</td>
                        <td class="amount">
                            {% if detail.debit_amount > 0 %}
                                {{ "{:,.2f}".format(detail.debit_amount|float) }}
                                {% set total_debit = total_debit + detail.debit_amount %}
                            {% endif %}
                        </td>
                        <td class="amount">
                            {% if detail.credit_amount > 0 %}
                                {{ "{:,.2f}".format(detail.credit_amount|float) }}
                                {% set total_credit = total_credit + detail.credit_amount %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                    {% endfor %}

                    <!-- 只在需要时添加少量空行保持最小美观 -->
                    {% if ns.row_count < 3 %}
                    {% for i in range(ns.row_count, 3) %}
                    <tr>
                        <td style="text-align: center;">{{ i + 1 }}</td>
                        <td class="summary-cell">&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endfor %}
                    {% endif %}

                    <!-- 合计行 -->
                    <tr class="total-row">
                        <td style="text-align: center;"><strong>合计</strong></td>
                        <td class="summary-cell"></td>
                        <td></td>
                        <td class="amount"><strong>{{ "{:,.2f}".format(total_debit|float) }}</strong></td>
                        <td class="amount"><strong>{{ "{:,.2f}".format(total_credit|float) }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 用友标准底部信息 -->
        <div class="voucher-bottom">
            <!-- 金额大写 -->
            <div class="amount-chinese-section">
                <span class="amount-chinese-label">金额大写：</span>
                <span class="amount-chinese-value">人民币零元整</span>
            </div>

            <!-- 附件信息 -->
            <div class="attachment-section">
                <div class="attachment-item">
                    <label>制单人：</label>
                    <span class="attachment-value">{{ voucher.created_by.username if voucher.created_by else '' }}</span>
                </div>
                <div class="attachment-item">
                    <label>制单日期：</label>
                    <span class="attachment-value">{{ voucher.created_at.strftime('%Y-%m-%d') if voucher.created_at else '' }}</span>
                </div>
                <div class="attachment-item">
                    <label>凭证状态：</label>
                    <span class="attachment-value">{{ voucher.status }}</span>
                </div>
            </div>
        </div>

        <!-- 用友标准签名栏 -->
        <div class="voucher-signature">
            <div class="signature-item">
                <span class="signature-label">制单</span>
                <span class="signature-line">{{ voucher.created_by.username if voucher.created_by else '' }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">审核</span>
                <span class="signature-line">{{ voucher.reviewed_by.username if voucher.reviewed_by else '' }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">记账</span>
                <span class="signature-line">{{ voucher.posted_by.username if voucher.posted_by else '' }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">出纳</span>
                <span class="signature-line"></span>
            </div>
        </div>

        <!-- 打印信息 -->
        <div style="margin-top: 20px; text-align: right; font-size: 10px; color: #666;">
            打印时间：{{ moment().format('YYYY-MM-DD HH:mm:ss') if moment else '' }}
            &nbsp;&nbsp;&nbsp;&nbsp;
            页码：第1页 共1页
        </div>
    </div>

    <script>
        // 中文大写金额转换
        function convertAmountToChinese(amount) {
            const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const units = ['', '拾', '佰', '仟'];
            const bigUnits = ['', '万', '亿'];

            if (amount === 0) return '零';

            let amountStr = Math.floor(amount * 100).toString();
            let result = '';
            let unitIndex = 0;
            let needZero = false;

            // 处理分
            if (amountStr.length >= 1) {
                let fen = parseInt(amountStr.slice(-1));
                if (fen > 0) {
                    result = digits[fen] + '分' + result;
                }
            }

            // 处理角
            if (amountStr.length >= 2) {
                let jiao = parseInt(amountStr.slice(-2, -1));
                if (jiao > 0) {
                    result = digits[jiao] + '角' + result;
                } else if (result && amountStr.length > 2) {
                    result = '零' + result;
                }
            }

            // 处理元及以上
            if (amountStr.length > 2) {
                let yuanStr = amountStr.slice(0, -2);
                if (yuanStr === '0') {
                    result = '零元' + result;
                } else {
                    result = convertIntegerPart(yuanStr) + '元' + result;
                }
            } else {
                result = '零元' + result;
            }

            return result || '零';
        }

        function convertIntegerPart(numStr) {
            const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const units = ['', '拾', '佰', '仟'];
            const bigUnits = ['', '万', '亿'];

            let result = '';
            let len = numStr.length;
            let zeroFlag = false;

            for (let i = 0; i < len; i++) {
                let digit = parseInt(numStr[i]);
                let unitPos = len - i - 1;
                let bigUnitPos = Math.floor(unitPos / 4);
                let smallUnitPos = unitPos % 4;

                if (digit === 0) {
                    zeroFlag = true;
                } else {
                    if (zeroFlag && result) {
                        result += '零';
                    }
                    result += digits[digit] + units[smallUnitPos];
                    zeroFlag = false;
                }

                if (smallUnitPos === 0 && bigUnitPos > 0) {
                    result += bigUnits[bigUnitPos];
                }
            }

            return result;
        }

        // 直接打印功能
        function openDirectPrint() {
            const printUrl = window.location.href.replace('preview=1', 'auto_print=1');
            window.open(printUrl, '_blank', 'width=800,height=600');
        }

        // 页面加载完成后更新金额大写
        document.addEventListener('DOMContentLoaded', function() {
            // 更新金额大写显示
            const amountElement = document.querySelector('.amount-chinese-value');
            if (amountElement) {
                const totalAmount = {{ total_debit|float }};
                if (totalAmount > 0) {
                    const chineseAmount = convertAmountToChinese(totalAmount);
                    amountElement.textContent = '人民币' + chineseAmount + '整';
                }
            }

            // 如果是通过打印链接打开的，自动打印
            {% if auto_print %}
            setTimeout(function() {
                window.print();
            }, 500);
            {% endif %}
        });

        // 打印后关闭窗口
        window.addEventListener('afterprint', function() {
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        });
    </script>
</body>
</html>
