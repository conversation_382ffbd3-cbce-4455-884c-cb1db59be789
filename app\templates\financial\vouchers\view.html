{% extends "financial/base.html" %}

{% block title %}查看财务凭证{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/finance-plus-theme.css') }}" nonce="{{ csp_nonce }}">
{% endblock %}

{% block financial_content %}
<div class="finance-plus-app">
    <div class="finance-plus-window">
        <!-- 窗口标题栏 -->
        <div class="finance-plus-header">
            <div class="finance-plus-title">
                <span class="icon">👁️</span>
                <span>查看记账凭证 - {{ voucher.voucher_number }}</span>
                <span class="status-badge status-{{ 'draft' if voucher.status == '草稿' else 'pending' if voucher.status == '待审核' else 'approved' if voucher.status == '已审核' else 'posted' }}">
                    {{ voucher.status }}
                </span>
            </div>
            <div class="finance-plus-controls">
                {% if voucher.status in ['草稿', '待审核'] %}
                <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" class="finance-plus-btn">
                    ✏️ 编辑
                </a>
                {% endif %}
                <a href="{{ url_for('financial.vouchers_index') }}" class="finance-plus-btn">
                    📋 列表
                </a>
                <button class="finance-plus-btn" onclick="window.history.back()">
                    ✕ 关闭
                </button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="finance-plus-toolbar">
            {% if voucher.status == '草稿' %}
            <button type="button" class="toolbar-btn primary" onclick="submitVoucherForReview({{ voucher.id }})">
                📤 提交审核
            </button>
            {% endif %}

            {% if voucher.status == '待审核' %}
            <form method="POST" action="{{ url_for('financial.review_voucher', id=voucher.id) }}"
                  style="display: inline;" onsubmit="return confirm('确定要审核通过此凭证吗？')">
                <button type="submit" class="toolbar-btn success">
                    ✅ 审核通过
                </button>
            </form>
            <button type="button" class="toolbar-btn danger" onclick="showRejectModal({{ voucher.id }})">
                ❌ 审核拒绝
            </button>
            {% endif %}

            <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}" class="toolbar-btn">
                📄 文本视图
            </a>

            <button class="toolbar-btn" onclick="previewVoucher()">👁️ 预览</button>
            <button class="toolbar-btn" onclick="printVoucher()">🖨️ 打印</button>
            <button class="toolbar-btn" onclick="exportVoucher()">📤 导出</button>

            <div class="toolbar-separator"></div>

            <div class="toolbar-status">
                <span>状态: {{ voucher.status }}</span>
                <span>创建: {{ voucher.creator.username if voucher.creator else '未知' }}</span>
                {% if voucher.reviewed_by %}
                <span>审核: {{ voucher.reviewer.username if voucher.reviewer else '未知' }}</span>
                {% endif %}
            </div>
        </div>
        <!-- 凭证信息栏 -->
        <div class="voucher-info-grid" style="background: #fafafa; padding: 16px; margin: 0 16px; border: 1px solid #e8e8e8; border-radius: 4px; display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; font-size: 12px;">
            <div class="info-item">
                <label style="color: var(--ThemeTextSencondaryColor); margin-bottom: 4px; display: block;">凭证号：</label>
                <span style="font-weight: 600; color: var(--ThemePrimaryColor);">{{ voucher.voucher_number }}</span>
            </div>
            <div class="info-item">
                <label style="color: var(--ThemeTextSencondaryColor); margin-bottom: 4px; display: block;">日期：</label>
                <span>{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</span>
            </div>
            <div class="info-item">
                <label style="color: var(--ThemeTextSencondaryColor); margin-bottom: 4px; display: block;">类型：</label>
                <span>{{ voucher.voucher_type }}</span>
            </div>
            <div class="info-item">
                <label style="color: var(--ThemeTextSencondaryColor); margin-bottom: 4px; display: block;">总金额：</label>
                <span style="font-weight: 600; color: var(--ThemeSuccessColor); font-family: 'Courier New', monospace;">¥{{ "{:,.2f}".format(voucher.total_amount) }}</span>
            </div>
            {% if voucher.attachment_count and voucher.attachment_count > 0 %}
            <div class="info-item">
                <label style="color: var(--ThemeTextSencondaryColor); margin-bottom: 4px; display: block;">附件：</label>
                <span>{{ voucher.attachment_count }} 个</span>
            </div>
            {% endif %}
        </div>

        <!-- 摘要信息 -->
        {% if voucher.summary %}
        <div style="margin: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #e6f7ff; border-radius: 4px;">
            <label style="color: var(--ThemeTextSencondaryColor); font-size: 12px; margin-bottom: 4px; display: block;">摘要：</label>
            <div style="color: var(--text-primary-color); font-size: 12px;">{{ voucher.summary }}</div>
        </div>
        {% endif %}

        <!-- 凭证明细表格 -->
        <div class="finance-plus-table-container" style="margin: 16px;">
            {% if details %}
            <table class="finance-plus-table">
                <thead>
                    <tr>
                        <th style="width: 60px;">行号</th>
                        <th style="width: 200px;">会计科目</th>
                        <th>摘要</th>
                        <th style="width: 120px;">借方金额</th>
                        <th style="width: 120px;">贷方金额</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in details %}
                    <tr>
                        <td style="text-align: center;">{{ detail.line_number }}</td>
                        <td>
                            <div style="font-weight: 500; color: var(--ThemePrimaryColor);">{{ detail.accounting_subject.code if detail.accounting_subject else '未知' }}</div>
                            <div style="font-size: 11px; color: var(--ThemeTextSencondaryColor);">{{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</div>
                        </td>
                        <td>{{ detail.summary or '' }}</td>
                        <td style="text-align: right; font-family: 'Courier New', monospace;">
                            {% if detail.debit_amount > 0 %}{{ "{:,.2f}".format(detail.debit_amount) }}{% endif %}
                        </td>
                        <td style="text-align: right; font-family: 'Courier New', monospace;">
                            {% if detail.credit_amount > 0 %}{{ "{:,.2f}".format(detail.credit_amount) }}{% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    <tr style="background: #fafafa; font-weight: 600;">
                        <td colspan="3" style="text-align: right;">合计：</td>
                        <td style="text-align: right; font-family: 'Courier New', monospace; color: var(--ThemeSuccessColor);">{{ "{:,.2f}".format(details|sum(attribute='debit_amount')) }}</td>
                        <td style="text-align: right; font-family: 'Courier New', monospace; color: var(--ThemeSuccessColor);">{{ "{:,.2f}".format(details|sum(attribute='credit_amount')) }}</td>
                    </tr>
                    <tr style="background: #f0f9ff;">
                        <td colspan="5" style="text-align: center; font-size: 12px; color: var(--ThemeTextSencondaryColor);">
                            金额大写：{{ voucher.total_amount|amount_to_chinese if voucher.total_amount else '零元整' }}
                        </td>
                    </tr>
                </tbody>
            </table>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">📄</div>
                <div class="empty-text">暂无凭证明细</div>
            </div>
            {% endif %}
        </div>

        <!-- 备注信息 -->
        {% if voucher.notes %}
        <div style="margin: 16px; padding: 12px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px;">
            <label style="color: var(--ThemeTextSencondaryColor); font-size: 12px; margin-bottom: 4px; display: block;">备注：</label>
            <div style="color: var(--text-primary-color); font-size: 12px;">{{ voucher.notes }}</div>
        </div>
        {% endif %}

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>创建时间: {{ voucher.created_at.strftime('%Y-%m-%d %H:%M') if voucher.created_at else '未知' }}</span>
            {% if voucher.reviewed_by %}
            <span>审核时间: {{ voucher.reviewed_at.strftime('%Y-%m-%d %H:%M') if voucher.reviewed_at else '未知' }}</span>
            {% endif %}
        </div>
    </div>
</div>

<!-- 拒绝审核模态框 -->
<div class="modal fade" id="rejectVoucherModal" tabindex="-1" role="dialog" aria-labelledby="rejectVoucherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: #F2F4F6; border-bottom: 1px solid #DDDDDD;">
                <h5 class="modal-title" id="rejectVoucherModalLabel" style="font-size: 14px; color: #333333;">拒绝审核凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="rejectVoucherForm" method="POST">
                <div class="modal-body" style="padding: 15px;">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        确定要拒绝审核此凭证吗？凭证将退回到草稿状态。
                    </div>
                    <div class="form-group">
                        <label for="rejectReason" class="form-label">拒绝原因：</label>
                        <textarea class="form-control" name="reject_reason" id="rejectReason" rows="3" placeholder="请填写拒绝原因..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer" style="background: #F8F9FA; border-top: 1px solid #DDDDDD;">
                    <button type="button" class="uf-btn" data-dismiss="modal">取消</button>
                    <button type="submit" class="uf-btn uf-status-action-reject">确认拒绝</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 好业财风格凭证查看页面

// 提交凭证审核
function submitVoucherForReview(voucherId) {
    if (confirm('确定要提交此凭证审核吗？')) {
        fetch(`/financial/vouchers/${voucherId}/submit-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('凭证已提交审核');
                window.location.reload();
            } else {
                alert('提交失败：' + data.message);
            }
        })
        .catch(error => {
            alert('提交失败，请重试');
        });
    }
}

// 显示拒绝审核模态框
function showRejectModal(voucherId) {
    const form = document.getElementById('rejectVoucherForm');
    form.action = `/financial/vouchers/${voucherId}/reject`;
    document.getElementById('rejectReason').value = '';
    $('#rejectVoucherModal').modal('show');
}

// 预览凭证
function previewVoucher() {
    const voucherId = {{ voucher.id }};
    const previewUrl = `/financial/vouchers/${voucherId}/print?preview=1`;
    window.open(previewUrl, '_blank', 'width=1000,height=700,scrollbars=yes');
}

// 打印凭证
function printVoucher() {
    const voucherId = {{ voucher.id }};
    const printUrl = `/financial/vouchers/${voucherId}/print?auto_print=1`;
    window.open(printUrl, '_blank', 'width=800,height=600');
}

// 导出凭证
function exportVoucher() {
    const voucherId = {{ voucher.id }};
    window.location.href = `/financial/vouchers/${voucherId}/export`;
}

// 键盘快捷键
$(document).ready(function() {
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printVoucher();
        }
        if (e.key === 'Escape') {
            window.history.back();
        }
    });
});
</script>
{% endblock %}
