/**
 * 凭证表格操作
 * 添加行、删除行、表格布局等功能
 */

// 添加新行
function addUFRow() {
    const $tbody = $('.uf-voucher-table tbody');
    const rowCount = $tbody.find('tr').length;
    
    const newRowHtml = `
        <tr class="uf-voucher-row">
            <td class="uf-sequence-cell">
                <span class="uf-line-number">${rowCount + 1}</span>
            </td>
            <td class="uf-summary-cell">
                <textarea class="uf-form-control uf-summary-input" 
                         placeholder="摘要" 
                         rows="1"></textarea>
            </td>
            <td class="uf-subject-cell">
                <div class="uf-subject-selector">
                    <div class="uf-subject-input-container">
                        <input type="text" class="uf-form-control uf-subject-input"
                               placeholder="输入科目编码/名称/拼音首字母"
                               autocomplete="off">
                        <div class="uf-subject-dropdown" style="display: none;">
                            <div class="uf-subject-dropdown-list">
                                <!-- 科目选项将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </td>
            <td class="uf-amount-cell uf-debit-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-debit-amount" 
                           placeholder="0.00" 
                           data-amount-type="debit"
                           autocomplete="off">
                    <span class="uf-amount-unit">元</span>
                </div>
            </td>
            <td class="uf-amount-cell uf-credit-cell">
                <div class="uf-amount-input-container">
                    <input type="text" class="uf-form-control uf-credit-amount" 
                           placeholder="0.00" 
                           data-amount-type="credit"
                           autocomplete="off">
                    <span class="uf-amount-unit">元</span>
                </div>
            </td>
            <td class="uf-operations-cell">
                <button type="button" class="uf-btn uf-btn-sm uf-add-row" title="添加行">
                    ➕
                </button>
                <button type="button" class="uf-btn uf-btn-sm uf-delete-row" title="删除行">
                    ➖
                </button>
            </td>
        </tr>
    `;

    $tbody.append(newRowHtml);
    updateUFLineNumbers();

    // 聚焦到新行的摘要输入框
    const $newRow = $tbody.find('tr').last();
    const $summaryInput = $newRow.find('.uf-summary-input');
    
    // 初始化新添加的摘要输入框的自动调整功能
    setTimeout(() => {
        autoResizeTextarea($summaryInput[0]);
        $summaryInput.focus();
    }, 100);

    console.log(`✅ 添加新行，当前共 ${$tbody.find('tr').length} 行`);
}

// 删除行
function deleteUFRow($row) {
    const $tbody = $('.uf-voucher-table tbody');
    const totalRows = $tbody.find('tr').length;

    // 至少保留一行
    if (totalRows <= 1) {
        showUFMessage('至少需要保留一行', 'warning');
        return;
    }

    // 确认删除
    if (hasRowData($row)) {
        if (!confirm('该行包含数据，确定要删除吗？')) {
            return;
        }
    }

    $row.remove();
    updateUFLineNumbers();
    updateTotals();

    console.log(`✅ 删除行，当前共 ${$tbody.find('tr').length} 行`);
}

// 检查行是否包含数据
function hasRowData($row) {
    const summary = $row.find('.uf-summary-input').val().trim();
    const subject = $row.find('.uf-subject-input').val().trim();
    const debit = $row.find('.uf-debit-amount').val().trim();
    const credit = $row.find('.uf-credit-amount').val().trim();

    return summary || subject || debit || credit;
}

// 清空行数据
function clearRowData($row) {
    $row.find('.uf-summary-input').val('');
    $row.find('.uf-subject-input').val('').removeData();
    $row.find('.uf-debit-amount').val('');
    $row.find('.uf-credit-amount').val('');
    
    // 重置样式
    $row.find('.uf-debit-amount, .uf-credit-amount').removeClass('has-value zero invalid large-amount');
}

// 复制行
function copyUFRow($row) {
    const $tbody = $('.uf-voucher-table tbody');
    const $newRow = $row.clone();
    
    // 清空新行的金额
    $newRow.find('.uf-debit-amount, .uf-credit-amount').val('');
    $newRow.find('.uf-debit-amount, .uf-credit-amount').removeClass('has-value zero invalid large-amount');
    
    // 插入到当前行后面
    $row.after($newRow);
    updateUFLineNumbers();
    
    // 聚焦到新行的借方金额
    setTimeout(() => {
        $newRow.find('.uf-debit-amount').focus();
    }, 100);

    console.log('✅ 复制行完成');
}

// 移动行
function moveRowUp($row) {
    const $prev = $row.prev('tr');
    if ($prev.length > 0) {
        $row.insertBefore($prev);
        updateUFLineNumbers();
        console.log('✅ 行上移完成');
    }
}

function moveRowDown($row) {
    const $next = $row.next('tr');
    if ($next.length > 0) {
        $row.insertAfter($next);
        updateUFLineNumbers();
        console.log('✅ 行下移完成');
    }
}

// 自动调整摘要文本区高度
function autoResizeTextarea(textarea) {
    if (!textarea) return;
    
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';
    
    // 计算所需高度
    const minHeight = 38; // 最小高度
    const maxHeight = 120; // 最大高度
    const scrollHeight = textarea.scrollHeight;
    
    // 设置新高度
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = newHeight + 'px';
    
    // 如果内容超过最大高度，显示滚动条
    if (scrollHeight > maxHeight) {
        textarea.style.overflowY = 'auto';
    } else {
        textarea.style.overflowY = 'hidden';
    }
}

// 初始化摘要文本区自动调整
function initSummaryAutoResize() {
    // 为所有摘要输入框添加事件监听
    $(document).on('input', '.uf-summary-input', function() {
        autoResizeTextarea(this);
        // 内容变化时可能需要重新调整表格布局
        clearTimeout(window.layoutTimer);
        window.layoutTimer = setTimeout(optimizeTableLayout, 300);
    });
    
    // 为所有摘要输入框添加键盘事件
    $(document).on('keydown', '.uf-summary-input', function(e) {
        // 延迟执行以确保内容已更新
        setTimeout(() => {
            autoResizeTextarea(this);
        }, 0);
    });
    
    // 添加粘贴事件处理
    $(document).on('paste', '.uf-summary-input', function() {
        setTimeout(() => {
            autoResizeTextarea(this);
        }, 10);
    });
    
    // 初始化现有的摘要输入框
    $('.uf-summary-input').each(function() {
        autoResizeTextarea(this);
    });
}

// 优化表格列宽自适应
function optimizeTableLayout() {
    const table = document.querySelector('.uf-voucher-table');
    if (!table) return;
    
    // 获取表格容器宽度
    const container = table.closest('.uf-voucher-table-container');
    if (!container) return;
    
    const containerWidth = container.offsetWidth;
    console.log('容器宽度:', containerWidth);
    
    // 如果容器宽度太小，使用自动布局
    if (containerWidth < 800) {
        table.classList.add('auto-layout');
        console.log('应用自动布局模式');
        return;
    } else {
        table.classList.remove('auto-layout');
    }
    
    // 计算各列的理想宽度
    const sequenceWidth = 50;
    const operationsWidth = 70;
    const amountWidth = Math.max(120, Math.min(160, containerWidth * 0.15));
    
    // 计算摘要和科目列的可用宽度
    const fixedWidth = sequenceWidth + operationsWidth + (amountWidth * 2) + 40; // 40px for borders and padding
    const availableWidth = containerWidth - fixedWidth;
    
    // 智能分配摘要和科目列宽度
    let summaryWidth, subjectWidth;
    
    if (availableWidth >= 400) {
        // 宽屏：摘要占更多空间
        summaryWidth = Math.max(200, availableWidth * 0.65);
        subjectWidth = Math.max(180, availableWidth * 0.35);
    } else if (availableWidth >= 300) {
        // 中等屏幕：平均分配
        summaryWidth = Math.max(150, availableWidth * 0.55);
        subjectWidth = Math.max(150, availableWidth * 0.45);
    } else {
        // 小屏幕：使用最小宽度
        summaryWidth = 150;
        subjectWidth = 150;
    }
    
    // 应用样式
    const style = document.createElement('style');
    style.textContent = `
        .uf-voucher-table:not(.auto-layout) .uf-col-sequence { 
            width: ${sequenceWidth}px !important; 
            min-width: ${sequenceWidth}px !important;
            max-width: ${sequenceWidth}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-summary { 
            width: ${summaryWidth}px !important; 
            min-width: ${Math.min(summaryWidth, 200)}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-subject { 
            width: ${subjectWidth}px !important; 
            min-width: ${Math.min(subjectWidth, 180)}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-debit,
        .uf-voucher-table:not(.auto-layout) .uf-col-credit { 
            width: ${amountWidth}px !important; 
            min-width: ${Math.min(amountWidth, 120)}px !important;
            max-width: ${amountWidth}px !important;
        }
        .uf-voucher-table:not(.auto-layout) .uf-col-operations { 
            width: ${operationsWidth}px !important; 
            min-width: ${operationsWidth}px !important;
            max-width: ${operationsWidth}px !important;
        }
    `;
    
    // 移除旧的动态样式
    const oldStyle = document.querySelector('#dynamic-table-style');
    if (oldStyle) {
        oldStyle.remove();
    }
    
    style.id = 'dynamic-table-style';
    document.head.appendChild(style);
    
    console.log('表格布局优化完成:', {
        容器宽度: containerWidth,
        序号列: sequenceWidth,
        摘要列: summaryWidth,
        科目列: subjectWidth,
        金额列: amountWidth,
        操作列: operationsWidth
    });
}

// 表格数据验证
function validateTableData() {
    const errors = [];
    let hasData = false;
    
    $('.uf-voucher-table tbody tr').each(function(index) {
        const $row = $(this);
        const rowNum = index + 1;
        
        const summary = $row.find('.uf-summary-input').val().trim();
        const subjectInput = $row.find('.uf-subject-input');
        const subjectId = subjectInput.data('subject-id');
        const debitAmount = parseUFAmount($row.find('.uf-debit-amount').val());
        const creditAmount = parseUFAmount($row.find('.uf-credit-amount').val());
        
        // 检查是否有数据
        if (summary || subjectId || debitAmount > 0 || creditAmount > 0) {
            hasData = true;
            
            // 验证必填字段
            if (!summary) {
                errors.push(`第${rowNum}行：摘要不能为空`);
            }
            
            if (!subjectId) {
                errors.push(`第${rowNum}行：请选择会计科目`);
            }
            
            if (debitAmount === 0 && creditAmount === 0) {
                errors.push(`第${rowNum}行：借方或贷方金额不能都为空`);
            }
            
            if (debitAmount > 0 && creditAmount > 0) {
                errors.push(`第${rowNum}行：借方和贷方金额不能同时有值`);
            }
        }
    });
    
    if (!hasData) {
        errors.push('请至少填写一行凭证数据');
    }
    
    return errors;
}

// 获取表格数据
function getTableData() {
    const data = [];
    
    $('.uf-voucher-table tbody tr').each(function() {
        const $row = $(this);
        
        const summary = $row.find('.uf-summary-input').val().trim();
        const subjectInput = $row.find('.uf-subject-input');
        const subjectId = subjectInput.data('subject-id');
        const debitAmount = parseUFAmount($row.find('.uf-debit-amount').val());
        const creditAmount = parseUFAmount($row.find('.uf-credit-amount').val());
        
        // 只收集有数据的行
        if (summary || subjectId || debitAmount > 0 || creditAmount > 0) {
            data.push({
                summary: summary,
                subject_id: subjectId,
                debit_amount: debitAmount,
                credit_amount: creditAmount
            });
        }
    });
    
    return data;
}

// 设置表格数据
function setTableData(data) {
    const $tbody = $('.uf-voucher-table tbody');
    
    // 清空现有行
    $tbody.empty();
    
    // 添加数据行
    data.forEach((rowData, index) => {
        addUFRow();
        const $row = $tbody.find('tr').last();
        
        // 设置数据
        $row.find('.uf-summary-input').val(rowData.summary || '');
        
        if (rowData.subject_id) {
            const subject = findSubjectById(rowData.subject_id);
            if (subject) {
                const $subjectInput = $row.find('.uf-subject-input');
                $subjectInput.val(`${subject.code} ${subject.name}`);
                $subjectInput.data('subject-id', subject.id);
                $subjectInput.data('subject-code', subject.code);
                $subjectInput.data('subject-name', subject.name);
            }
        }
        
        if (rowData.debit_amount > 0) {
            $row.find('.uf-debit-amount').val(rowData.debit_amount).trigger('blur');
        }
        
        if (rowData.credit_amount > 0) {
            $row.find('.uf-credit-amount').val(rowData.credit_amount).trigger('blur');
        }
    });
    
    // 确保至少有一行
    if (data.length === 0) {
        addUFRow();
    }
    
    // 更新合计
    updateTotals();
}

// 将主要函数暴露到全局作用域
window.addUFRow = addUFRow;
window.deleteUFRow = deleteUFRow;
window.insertRowAbove = insertRowAbove;
window.updateUFLineNumbers = updateUFLineNumbers;
window.autoResizeTextarea = autoResizeTextarea;
window.initSummaryAutoResize = initSummaryAutoResize;
window.optimizeTableLayout = optimizeTableLayout;
window.loadVoucherData = loadVoucherData;
