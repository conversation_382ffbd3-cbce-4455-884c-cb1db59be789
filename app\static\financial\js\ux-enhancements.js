/**
 * 用户体验增强功能
 * 快捷键、消息提示、帮助系统等
 */

// 键盘快捷键支持
function initUFKeyboardShortcuts() {
    $(document).on('keydown', function(e) {
        // Ctrl+S: 保存凭证
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (voucherMode !== 'view') {
                saveVoucher();
            }
            return false;
        }

        // Ctrl+P: 打印凭证
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printVoucher();
            return false;
        }

        // Ctrl+N: 新建凭证
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            newVoucher();
            return false;
        }

        // F1: 帮助
        if (e.key === 'F1') {
            e.preventDefault();
            showUFHelp();
            return false;
        }

        // F9: 借贷平衡检查
        if (e.key === 'F9') {
            e.preventDefault();
            checkBalance();
            return false;
        }

        // Alt+Left: 上一张凭证
        if (e.altKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            navigateToPrevious();
            return false;
        }

        // Alt+Right: 下一张凭证
        if (e.altKey && e.key === 'ArrowRight') {
            e.preventDefault();
            navigateToNext();
            return false;
        }

        // Escape: 取消当前操作
        if (e.key === 'Escape') {
            e.preventDefault();
            // 关闭模态框或取消编辑
            if ($('.uf-modal:visible').length > 0) {
                closeSubjectModal();
            }
            return false;
        }
    });
}

// 显示帮助信息
function showUFHelp() {
    const helpContent = `
        <div class="uf-help-content" style="text-align: left; line-height: 1.6;">
            <h3 style="margin-bottom: 15px;">🔧 快捷键说明</h3>
            <div class="uf-help-section" style="margin-bottom: 15px;">
                <h4 style="margin-bottom: 8px; color: #333;">基本操作</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+S</kbd> - 保存凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+P</kbd> - 打印凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Ctrl+N</kbd> - 新建凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">F9</kbd> - 借贷平衡检查</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Esc</kbd> - 取消当前操作</li>
                </ul>
            </div>
            <div class="uf-help-section" style="margin-bottom: 15px;">
                <h4 style="margin-bottom: 8px; color: #333;">凭证导航</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Alt+←</kbd> - 上一张凭证</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Alt+→</kbd> - 下一张凭证</li>
                </ul>
            </div>
            <div class="uf-help-section">
                <h4 style="margin-bottom: 8px; color: #333;">表格操作</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Tab</kbd> - 移动到下一个单元格</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Shift+Tab</kbd> - 移动到上一个单元格</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">Enter</kbd> - 移动到下一行</li>
                    <li><kbd style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">F7</kbd> - 选择会计科目</li>
                </ul>
            </div>
            <div class="uf-help-section" style="margin-top: 15px;">
                <h4 style="margin-bottom: 8px; color: #333;">科目输入技巧</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>输入科目编码：如 <code>1001</code></li>
                    <li>输入科目名称：如 <code>现金</code></li>
                    <li>输入拼音首字母：如 <code>xj</code> 匹配 <code>现金</code></li>
                    <li>支持模糊搜索和连续输入</li>
                </ul>
            </div>
        </div>
    `;

    showUFMessage(helpContent, 'info', 10000);
}

// 显示用友风格消息提示
function showUFMessage(message, type = 'info', duration = 3000) {
    // 移除现有消息
    $('.uf-message').remove();

    // 创建消息元素
    const messageClass = `uf-message uf-message-${type}`;
    const iconMap = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    const messageHtml = `
        <div class="${messageClass}" style="
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border: 2px solid ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196F3'};
            border-radius: 4px;
            padding: 12px 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 13px;
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        ">
            <span style="margin-right: 8px;">${iconMap[type] || 'ℹ️'}</span>
            <span>${message}</span>
            <button onclick="$(this).parent().remove()" style="
                float: right;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                margin-left: 10px;
                color: #999;
            ">&times;</button>
        </div>
    `;

    $('body').append(messageHtml);

    // 自动移除消息
    setTimeout(() => {
        $('.uf-message').fadeOut(300, function() {
            $(this).remove();
        });
    }, duration);
}

// 凭证操作函数
function saveVoucher() {
    console.log('💾 保存凭证...');
    
    // 验证数据
    const errors = validateTableData();
    if (errors.length > 0) {
        showUFMessage(`数据验证失败：\n${errors.join('\n')}`, 'error', 5000);
        return;
    }
    
    // 检查借贷平衡
    if (!checkBalance()) {
        showUFMessage('借贷不平衡，无法保存', 'error');
        return;
    }
    
    // 获取表格数据
    const tableData = getTableData();
    
    // 获取凭证基本信息
    const voucherData = {
        voucher_type: $('#voucher_type').val(),
        voucher_date: $('#voucher_date').val(),
        summary: $('#voucher_summary').val(),
        attachment_count: $('#attachment_count').val() || 0,
        details: tableData
    };
    
    // 发送保存请求
    const url = voucherMode === 'edit' ? `/financial/vouchers/${currentVoucherId}/edit` : '/financial/vouchers/create';
    const method = 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        body: JSON.stringify(voucherData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showUFMessage('凭证保存成功', 'success');
            if (voucherMode === 'create') {
                // 跳转到编辑页面
                window.location.href = `/financial/vouchers/${data.voucher_id}/edit`;
            }
        } else {
            showUFMessage(`保存失败：${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showUFMessage('保存失败，请检查网络连接', 'error');
    });
}

function printVoucher() {
    console.log('🖨️ 打印凭证...');
    
    if (voucherMode === 'create') {
        showUFMessage('请先保存凭证再打印', 'warning');
        return;
    }
    
    const printUrl = `/financial/vouchers/${currentVoucherId}/print`;
    window.open(printUrl, '_blank');
}

function newVoucher() {
    console.log('📄 新建凭证...');
    
    if (hasUnsavedChanges()) {
        if (confirm('当前有未保存的修改，确定要新建凭证吗？')) {
            window.location.href = '/financial/vouchers/create';
        }
    } else {
        window.location.href = '/financial/vouchers/create';
    }
}

function checkBalance() {
    console.log('⚖️ 检查借贷平衡...');
    
    let debitTotal = 0;
    let creditTotal = 0;
    
    $('.uf-debit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        if (value > 0) debitTotal += value;
    });
    
    $('.uf-credit-amount').each(function() {
        const value = parseUFAmount($(this).val());
        if (value > 0) creditTotal += value;
    });
    
    const isBalanced = Math.abs(debitTotal - creditTotal) < 0.01;
    const difference = Math.abs(debitTotal - creditTotal);
    
    if (isBalanced) {
        showUFMessage(`✅ 借贷平衡\n借方合计：${formatUFAmountDisplay(debitTotal)}\n贷方合计：${formatUFAmountDisplay(creditTotal)}`, 'success');
    } else {
        showUFMessage(`❌ 借贷不平衡\n借方合计：${formatUFAmountDisplay(debitTotal)}\n贷方合计：${formatUFAmountDisplay(creditTotal)}\n差额：${formatUFAmountDisplay(difference)}`, 'error');
    }
    
    return isBalanced;
}

function navigateToPrevious() {
    console.log('⬅️ 上一张凭证...');
    // 这里可以实现凭证导航逻辑
    showUFMessage('上一张凭证功能开发中...', 'info');
}

function navigateToNext() {
    console.log('➡️ 下一张凭证...');
    // 这里可以实现凭证导航逻辑
    showUFMessage('下一张凭证功能开发中...', 'info');
}

// 自动保存功能
let autoSaveTimer;
function enableAutoSave(interval = 300000) { // 5分钟自动保存
    if (voucherMode === 'view') return;
    
    autoSaveTimer = setInterval(() => {
        if (hasUnsavedChanges()) {
            console.log('🔄 自动保存...');
            saveVoucher();
        }
    }, interval);
}

function disableAutoSave() {
    if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
        autoSaveTimer = null;
    }
}

// 数据恢复功能
function saveToLocalStorage() {
    if (voucherMode === 'view') return;
    
    const data = {
        voucher_type: $('#voucher_type').val(),
        voucher_date: $('#voucher_date').val(),
        summary: $('#voucher_summary').val(),
        attachment_count: $('#attachment_count').val(),
        details: getTableData(),
        timestamp: new Date().getTime()
    };
    
    localStorage.setItem('uf_voucher_draft', JSON.stringify(data));
}

function loadFromLocalStorage() {
    const saved = localStorage.getItem('uf_voucher_draft');
    if (!saved) return null;
    
    try {
        const data = JSON.parse(saved);
        // 检查是否是最近的数据（24小时内）
        if (new Date().getTime() - data.timestamp < 24 * 60 * 60 * 1000) {
            return data;
        }
    } catch (e) {
        console.error('解析本地存储数据失败:', e);
    }
    
    return null;
}

function clearLocalStorage() {
    localStorage.removeItem('uf_voucher_draft');
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时保存数据
        saveToLocalStorage();
    } else {
        // 页面显示时检查是否有更新
        console.log('页面重新获得焦点');
    }
});

// 定期保存到本地存储
setInterval(saveToLocalStorage, 30000); // 30秒保存一次

// 页面加载时检查本地存储
$(document).ready(function() {
    if (voucherMode === 'create') {
        const savedData = loadFromLocalStorage();
        if (savedData && savedData.details && savedData.details.length > 0) {
            if (confirm('检测到未保存的凭证草稿，是否恢复？')) {
                // 恢复数据
                $('#voucher_type').val(savedData.voucher_type);
                $('#voucher_date').val(savedData.voucher_date);
                $('#voucher_summary').val(savedData.summary);
                $('#attachment_count').val(savedData.attachment_count);
                setTableData(savedData.details);
                
                showUFMessage('草稿数据已恢复', 'success');
            } else {
                clearLocalStorage();
            }
        }
    }
});

// 将主要函数暴露到全局作用域
window.initUFKeyboardShortcuts = initUFKeyboardShortcuts;
window.showUFMessage = showUFMessage;
window.showUFHelp = showUFHelp;
window.hasUnsavedChanges = hasUnsavedChanges;
window.navigateToFirst = navigateToFirst;
window.navigateToPrevious = navigateToPrevious;
window.navigateToNext = navigateToNext;
window.navigateToLast = navigateToLast;
window.navigateToVoucher = navigateToVoucher;
window.printUFVoucher = printUFVoucher;
window.copyVoucher = copyVoucher;
window.reverseVoucher = reverseVoucher;
window.reviewVoucher = reviewVoucher;
window.unReviewVoucher = unReviewVoucher;
window.enableAutoSave = enableAutoSave;
window.disableAutoSave = disableAutoSave;
window.saveToLocalStorage = saveToLocalStorage;
window.loadFromLocalStorage = loadFromLocalStorage;
window.clearLocalStorage = clearLocalStorage;
