/* static/financial/css/voucher-style.css */

/* ===== 凭证页面专用样式重置 - 直接使用具体值 ===== */

/* 页面基础重置 */
body {
    margin: 0 !important;
    padding: 0 !important;
    font-family: "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif !important;
    background: #f8f9fa !important;
    line-height: 1.4 !important;
    font-size: 13px !important;
}

/* 移除外部容器干扰 */
.uf-financial-content {
    padding: 0 !important;
    background: transparent !important;
    margin: 0 !important;
}

/* ===== 强制凭证信息栏单行显示 - 使用具体值 ===== */

/* 凭证信息栏容器 */
.uf-voucher-info {
    background: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 8px 16px !important;
    overflow: hidden !important;
    display: block !important;
}

/* 信息行 - 强制单行显示 */
.uf-info-row {
    display: flex !important;
    gap: 16px !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
    justify-content: flex-start !important;
    white-space: nowrap !important;
    overflow-x: auto !important;
    min-height: 32px !important;
    width: 100% !important;
}

/* 表单组 - 强制内联显示 */
.uf-form-group {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    margin: 0 !important;
}

/* 表单标签 */
.uf-form-label {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: #6c757d !important;
    margin: 0 !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 凭证类型选择器 */
.uf-voucher-type {
    width: 50px !important;
    text-align: center !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    padding: 4px 6px !important;
    flex-shrink: 0 !important;
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    background: #fff !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 凭证号输入框 */
.uf-voucher-number {
    width: 80px !important;
    text-align: center !important;
    font-weight: 600 !important;
    padding: 4px 6px !important;
    flex-shrink: 0 !important;
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    background: #fff !important;
    font-size: 13px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 日期输入框 */
.uf-voucher-date {
    width: 120px !important;
    padding: 4px 6px !important;
    flex-shrink: 0 !important;
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    background: #fff !important;
    font-size: 13px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 附件数量输入框 */
.uf-attachment-count {
    width: 50px !important;
    text-align: center !important;
    padding: 4px 6px !important;
    flex-shrink: 0 !important;
    border: 1px solid #ced4da !important;
    border-radius: 3px !important;
    background: #fff !important;
    font-size: 13px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 制单人文本 */
.uf-form-text {
    color: #6c757d !important;
    font-weight: 500 !important;
    padding: 4px 6px !important;
    font-size: 13px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* ===== 凭证编辑器核心样式强制重置 ===== */

/* 编辑器容器 */
.uf-voucher-editor {
    background: var(--uf-gray-50) !important;
    min-height: 100vh !important;
    padding: 8px !important;
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* 凭证窗口 */
.uf-voucher-window {
    background: var(--uf-white) !important;
    border: 1px solid var(--uf-gray-300) !important;
    border-radius: 4px !important;
    box-shadow: var(--uf-box-shadow) !important;
    margin: 0 auto !important;
    max-width: 1400px !important;
    display: flex !important;
    flex-direction: column !important;
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
}

/* ===== 表格样式强制重置 ===== */

/* 凭证表格 */
.uf-voucher-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: var(--uf-font-size) !important;
    background: var(--uf-white) !important;
    table-layout: fixed !important;
    font-family: var(--uf-font-family) !important;
}

/* 表格单元格 */
.uf-voucher-table th,
.uf-voucher-table td {
    border: 1px solid var(--uf-gray-300) !important;
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    padding: 1px !important;
}

/* 表格标题 */
.uf-voucher-table th {
    background: var(--uf-gray-100) !important;
    font-weight: 600 !important;
    text-align: center !important;
    color: var(--uf-gray-700) !important;
    height: 32px !important;
}

/* ===== 表单控件样式强制重置 ===== */

/* 通用表单控件 */
.uf-voucher-editor .uf-form-control {
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    border: 1px solid var(--uf-gray-300) !important;
    border-radius: var(--uf-border-radius) !important;
    background: var(--uf-white) !important;
    color: var(--uf-gray-700) !important;
    outline: none !important;
    box-sizing: border-box !important;
}

/* 表单控件聚焦状态 */
.uf-voucher-editor .uf-form-control:focus {
    border-color: var(--uf-primary) !important;
    background: var(--uf-input-focus) !important;
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2) !important;
}

/* 表格内输入框特殊处理 */
.uf-voucher-table input,
.uf-voucher-table textarea,
.uf-voucher-table select {
    border: none !important;
    background: transparent !important;
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    outline: none !important;
}

.uf-voucher-table input:focus,
.uf-voucher-table textarea:focus,
.uf-voucher-table select:focus {
    background: var(--uf-input-focus) !important;
    border: 1px solid var(--uf-primary) !important;
    border-radius: 2px !important;
}

/* ===== 按钮样式强制重置 ===== */

/* 通用按钮 */
.uf-voucher-editor .uf-btn {
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    padding: 6px 12px !important;
    border: 1px solid var(--uf-gray-300) !important;
    border-radius: var(--uf-border-radius) !important;
    background: var(--uf-white) !important;
    color: var(--uf-gray-700) !important;
    cursor: pointer !important;
    transition: all 0.15s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    text-decoration: none !important;
    outline: none !important;
    box-sizing: border-box !important;
}

/* 按钮悬停状态 */
.uf-voucher-editor .uf-btn:hover {
    background: var(--uf-gray-50) !important;
    border-color: var(--uf-primary) !important;
}

/* 主要按钮 */
.uf-voucher-editor .uf-btn-primary {
    background: var(--uf-primary) !important;
    color: var(--uf-white) !important;
    border-color: var(--uf-primary) !important;
}

.uf-voucher-editor .uf-btn-primary:hover {
    background: var(--uf-primary-dark) !important;
    border-color: var(--uf-primary-dark) !important;
}

/* 小按钮 */
.uf-btn-sm {
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* ===== 调试样式 - 确保信息栏单行显示 ===== */

/* 最高优先级样式重置 */
div.uf-voucher-info div.uf-info-row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    gap: 16px !important;
    width: 100% !important;
    overflow-x: auto !important;
    white-space: nowrap !important;
}

div.uf-voucher-info div.uf-info-row div.uf-form-group {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 4px !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
}

/* 强制所有表单控件内联 */
.uf-voucher-info * {
    box-sizing: border-box !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
    font-size: 13px !important;
}

/* ===== 智能科目选择器样式 ===== */

/* 科目输入容器 */
.uf-subject-input-container {
    position: relative !important;
    width: 100% !important;
}

/* 科目输入框 */
.uf-subject-input {
    width: 100% !important;
    padding: 4px 8px !important;
    border: none !important;
    background: transparent !important;
    font-size: 13px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
    outline: none !important;
    cursor: text !important;
}

.uf-subject-input:focus {
    background: #e3f2fd !important;
    border: 1px solid #1976d2 !important;
    border-radius: 2px !important;
}

.uf-subject-input.has-value {
    background: #f8f9fa !important;
    color: #1976d2 !important;
    font-weight: 500 !important;
}

/* 下拉选择框 */
.uf-subject-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: #fff !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    z-index: 1000 !important;
    max-height: 200px !important;
    overflow-y: auto !important;
}

/* 下拉选项列表 */
.uf-subject-dropdown-list {
    padding: 4px 0 !important;
}

/* 下拉选项 */
.uf-subject-option {
    padding: 6px 12px !important;
    cursor: pointer !important;
    font-size: 13px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
    border-bottom: 1px solid #f1f3f4 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.uf-subject-option:hover {
    background: #e3f2fd !important;
    color: #1976d2 !important;
}

.uf-subject-option.selected {
    background: #1976d2 !important;
    color: #fff !important;
}

.uf-subject-option:last-child {
    border-bottom: none !important;
}

/* 科目编码 */
.uf-subject-option-code {
    font-weight: 600 !important;
    color: #1976d2 !important;
    margin-right: 8px !important;
    min-width: 60px !important;
}

.uf-subject-option.selected .uf-subject-option-code {
    color: #fff !important;
}

/* 科目名称 */
.uf-subject-option-name {
    flex: 1 !important;
    text-align: left !important;
}

/* 无匹配结果 */
.uf-subject-no-results {
    padding: 12px !important;
    text-align: center !important;
    color: #6c757d !important;
    font-style: italic !important;
}

/* 搜索提示 */
.uf-subject-search-hint {
    padding: 8px 12px !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    font-size: 12px !important;
    color: #6c757d !important;
}

/* ===== 用友风格金额格式化样式 ===== */

/* 金额输入框 - 用友风格 */
.uf-amount-input {
    width: 100% !important;
    height: 36px !important;
    text-align: right !important;
    font-family: 'Times New Roman', 'Courier New', monospace !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    padding: 6px 8px !important;
    border: none !important;
    background: transparent !important;
    box-sizing: border-box !important;
    outline: none !important;
    letter-spacing: 1px !important;
}

.uf-amount-input:focus {
    background: #e3f2fd !important;
    border: 1px solid #1976d2 !important;
    border-radius: 2px !important;
}

/* 借方金额 - 蓝色 */
.uf-debit-amount {
    color: #1976d2 !important;
}

.uf-debit-amount.has-value {
    background: #e8f4fd !important;
    font-weight: 600 !important;
}

/* 贷方金额 - 红色 */
.uf-credit-amount {
    color: #d32f2f !important;
}

.uf-credit-amount.has-value {
    background: #ffeaea !important;
    font-weight: 600 !important;
}

/* 金额容器 */
.uf-amount-input-container {
    position: relative !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
}

/* 金额单位显示 */
.uf-amount-unit {
    position: absolute !important;
    right: 2px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 11px !important;
    color: #6c757d !important;
    pointer-events: none !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 金额验证状态 */
.uf-amount-input.invalid {
    background: #ffebee !important;
    border: 1px solid #f44336 !important;
    color: #f44336 !important;
}

/* 零金额样式 */
.uf-amount-input.zero {
    color: #9e9e9e !important;
    font-style: italic !important;
}

/* 大金额警告 */
.uf-amount-input.large-amount {
    background: #fff3e0 !important;
    border: 1px solid #ff9800 !important;
}

/* ===== 大写金额行和合计行样式 ===== */

/* 大写金额行 */
.uf-chinese-amount-row {
    background: #f8f9fa !important;
    border-top: 2px solid #1976d2 !important;
}

.uf-chinese-amount-label {
    text-align: center !important;
    font-weight: 600 !important;
    color: #1976d2 !important;
    font-size: 13px !important;
    padding: 8px 4px !important;
    vertical-align: middle !important;
    border: 1px solid #ced4da !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

.uf-chinese-amount-cell {
    padding: 8px 12px !important;
    border: 1px solid #ced4da !important;
    background: #fff !important;
}

.uf-chinese-amount-container {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.uf-chinese-prefix {
    font-weight: 500 !important;
    color: #495057 !important;
    font-size: 13px !important;
    white-space: nowrap !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

.uf-chinese-amount-text {
    font-weight: 600 !important;
    color: #1976d2 !important;
    font-size: 14px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
    flex: 1 !important;
    min-height: 20px !important;
}

/* 合计行样式 */
.uf-totals-row {
    background: #e3f2fd !important;
    border-top: 1px solid #1976d2 !important;
}

.uf-totals-label {
    text-align: center !important;
    font-weight: 600 !important;
    color: #1976d2 !important;
    font-size: 13px !important;
    padding: 8px 4px !important;
    vertical-align: middle !important;
    border: 1px solid #ced4da !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

.uf-totals-amount {
    font-family: 'Times New Roman', 'Courier New', monospace !important;
    font-weight: 700 !important;
    font-size: 15px !important;
    color: #1976d2 !important;
    letter-spacing: 1px !important;
    text-align: right !important;
    padding: 8px 12px !important;
    border: 1px solid #ced4da !important;
    background: #fff !important;
}

.uf-totals-amount .uf-currency {
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
    margin-right: 4px !important;
    font-weight: 500 !important;
}

/* 平衡指示器样式 */
.uf-balance-indicator {
    text-align: center !important;
    padding: 4px !important;
}

.uf-balance-text {
    font-size: 12px !important;
    font-weight: 600 !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

.uf-balance-text.balanced {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.uf-balance-text.unbalanced {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}