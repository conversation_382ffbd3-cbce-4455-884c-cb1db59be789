{% extends "base.html" %}

{% block title %}财务管理{% endblock %}

{% block styles %}
{{ super() }}
<!-- 引入用友财务专业主题样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}?v=2.0.0">
<style>
    /* 财务模块专用样式 - 基于用友主题 */

    /* 财务模块全局样式 - 使用用友标准 */
    .uf-financial-content {
        font-family: var(--uf-font-family);
        background: var(--uf-light);
        color: #333;
        line-height: var(--uf-line-height);
        padding: 12px;
        font-size: var(--uf-font-size);
    }

    /* 确保凭证编辑器不受全局样式影响 */
    .uf-voucher-editor {
        padding: 8px !important;
        background: var(--uf-gray-50) !important;
        font-family: var(--uf-font-family) !important;
        font-size: var(--uf-font-size) !important;
    }

    /* 用友风格面包屑导航 */
    .uf-breadcrumb {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 12px;
        font-size: var(--uf-font-size);
        padding: 4px 0;
    }

    .uf-breadcrumb-item {
        color: #666;
        font-size: var(--uf-font-size);
    }

    .uf-breadcrumb-item a {
        color: var(--uf-primary);
        text-decoration: none;
        transition: var(--uf-transition);
    }

    .uf-breadcrumb-item a:hover {
        color: var(--uf-primary-dark);
        text-decoration: underline;
    }

    .uf-breadcrumb-item.active {
        color: #333;
        font-weight: 500;
    }

    .uf-breadcrumb-separator {
        color: #999;
        margin: 0 2px;
    }

    /* 用友风格工具栏 */
    .uf-financial-toolbar {
        background: var(--uf-toolbar-bg);
        padding: 8px 12px;
        border-radius: var(--uf-border-radius);
        box-shadow: var(--uf-box-shadow);
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid var(--uf-border);
        font-size: var(--uf-font-size);
    }

    .uf-financial-toolbar-title {
        font-size: 13px;
        font-weight: 600;
        color: var(--uf-primary);
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .uf-financial-toolbar-actions {
        display: flex;
        gap: 8px;
    }

    /* 财务模块特定样式覆盖 - 基于用友主题 */


</style>
{% block financial_css %}{% endblock %}
{% endblock %}

{% block content %}
<!-- 检查是否为凭证编辑页面，如果是则不使用标准容器 -->
{% if request.endpoint and 'voucher' in request.endpoint and ('create' in request.endpoint or 'edit' in request.endpoint or 'view' in request.endpoint) %}
    <!-- 凭证页面直接渲染内容，不使用标准容器 -->
    {% block financial_content %}{% endblock %}
{% else %}
<div class="uf-financial-content">
    <!-- 用友风格面包屑导航 -->
    <nav class="uf-breadcrumb">
        <span class="uf-breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></span>
        <span class="uf-breadcrumb-separator">/</span>
        <span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></span>
        {% if self.breadcrumb() %}
        <span class="uf-breadcrumb-separator">/</span>
        {% block breadcrumb %}{% endblock %}
        {% endif %}
    </nav>

    <!-- 用友风格工具栏 -->
    <div class="uf-financial-toolbar">
        <div class="uf-financial-toolbar-title">
            <i class="fas fa-calculator"></i>
            {% block page_title %}财务管理{% endblock %}
        </div>
        <div class="uf-financial-toolbar-actions">
            {% block page_actions %}{% endblock %}
        </div>
    </div>

    <!-- 页面内容 -->
    {{ self.financial_content() }}
</div>
{% endif %}
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 财务系统通用函数 - 基于用友主题

// 金额格式化
function formatAmount(amount) {
    if (amount === null || amount === undefined || amount === '') return '0.00';
    const num = parseFloat(amount);
    if (isNaN(num)) return '0.00';
    return num.toFixed(2);
}

// 金额显示（带货币符号）
function formatAmountDisplay(amount) {
    const formatted = formatAmount(amount);
    return '<span class="uf-currency">¥</span><span class="uf-amount">' + formatted + '</span>';
}

// 初始化用友财务界面
document.addEventListener('DOMContentLoaded', function() {
    // 初始化用友表格样式
    document.querySelectorAll('.uf-table tbody tr').forEach(function(row) {
        row.addEventListener('click', function() {
            this.classList.toggle('selected');
        });
    });
});
</script>
{% block financial_js %}{% endblock %}
{% endblock %}
