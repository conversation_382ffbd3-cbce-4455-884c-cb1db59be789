{% extends "base.html" %}

{% block title %}请求过多 - 429{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h1 class="display-1 text-warning">429</h1>
                    <h4 class="card-title">请求过多</h4>
                    <p class="card-text">您的请求过于频繁，请稍后重试。</p>
                    <div class="alert alert-info">
                        <i class="fas fa-clock"></i>
                        <strong>建议等待时间：</strong> 1分钟后重试
                    </div>
                    <div class="mt-4">
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-redo"></i> 重新尝试
                        </button>
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 60秒后自动刷新页面
setTimeout(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}
